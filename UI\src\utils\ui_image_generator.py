#!/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :ui_image_generator.py
# @Time      :2025/6/5 00:38
# <AUTHOR>
# @Function  :
from PIL import Image, ImageDraw, ImageFont
import os

# --- 配置参数 ---
# 定义图标和文字之间的默认间距
ICON_TEXT_PADDING = 12 * 2  # 像素翻倍
# 默认的图标大小 (保持翻倍，以匹配整体高分辨率)
DEFAULT_ICON_SIZE = (24 * 2, 24 * 2)
# 默认的文字颜色
DEFAULT_TEXT_COLOR = (100, 100, 100, 255)  # 灰色
# 标题文字颜色 (例如，模型名称和新加入的“模型信息卡”标题)
TITLE_COLOR = (50, 50, 50, 255)  # 深灰色
# 状态文字颜色
STATUS_SUCCESS_COLOR = (40, 170, 70, 255)  # 绿色
STATUS_FAIL_COLOR = (220, 50, 50, 255)  # 红色
STATUS_UNKNOWN_COLOR = (150, 150, 150, 255)  # 未知状态颜色


# --- 辅助函数：绘制带图标的文本 ---
def draw_icon_and_text(
        draw_obj,
        canvas,
        icon_path,
        text,
        font,
        position,  # (x, y) 文本和图标整体的左上角位置
        text_color,
        icon_size=DEFAULT_ICON_SIZE,
        icon_text_padding=ICON_TEXT_PADDING
):
    """
    在画布上绘制一个图标和紧随其后的文本。

    Args:
        draw_obj (ImageDraw.Draw): Pillow 的 Draw 对象。
        canvas (Image.Image): 画布图像。
        icon_path (str): 图标文件的完整路径。
        text (str): 要绘制的文本内容。
        font (ImageFont.FreeTypeFont): 用于绘制文本的字体对象。
        position (tuple): (x, y) 文本和图标整体的左上角坐标。
        text_color (tuple): 文本颜色 (R, G, B, A)。
        icon_size (tuple): 图标的尺寸 (width, height)。
        icon_text_padding (int): 图标和文本之间的像素间距。
    """
    icon = None
    if icon_path and os.path.exists(icon_path):
        try:
            icon = Image.open(icon_path).convert("RGBA")
            # 使用 Image.LANCZOS 进行高质量缩放
            icon = icon.resize(icon_size, Image.LANCZOS)
        except Exception as e:
            print(f"警告: 处理图标文件 '{icon_path}' 时发生错误: {e}。将跳过此图标。")
            icon = None

    current_x = int(position[0])
    current_y = int(position[1])

    # 获取文本的边界框以计算其高度和垂直中心
    # getbbox returns (left, top, right, bottom) relative to text origin
    text_bbox = font.getbbox(text)
    # text_height = text_bbox[3] - text_bbox[1] # 实际文本高度 (从基线到顶部)

    # 计算图标的y坐标，使其垂直居中于文本的“视觉中心”
    # current_y 是文本的左上角绘制位置
    # 文本的垂直中心相对于 current_y = current_y + (text_bbox[3] + text_bbox[1]) / 2
    # icon_y = (文本垂直中心) - (icon高度 / 2)
    icon_y = current_y + (text_bbox[3] + text_bbox[1]) / 2 - icon_size[1] / 2

    # 强制转换为整数
    icon_y = int(icon_y)

    if icon:
        canvas.paste(icon, (current_x, icon_y), icon)
        current_x += icon_size[0] + icon_text_padding

    # 绘制文本时，y坐标保持不变，因为它已经是整个图标+文本块的左上角
    draw_obj.text(
        (current_x, current_y),
        text,
        fill=text_color,
        font=font
    )


def generate_model_card(
        image_path,  # 图片路径
        model_name,  # 模型名称
        train_date,  # 训练日期
        model_version,  # 模型版本
        model_format,  # 模型格式
        file_size,  # 文件大小
        device_used,  # 使用设备
        author_name,  # 作者
        status,  # 模型状态: "success", "fail" 或 "unknown"
        font_path_bold="LXGWWenKai-Bold.ttf",  # 粗体字体路径
        icons_dir="icons"  # 图标目录
):
    """
    根据提供的模型信息生成一个卡片图像。

    Args:
        image_path (str): 左侧要显示的图片路径。
        model_name (str): 模型名称。
        train_date (str): 训练日期。
        model_version (str): 模型版本。
        model_format (str): 模型格式。
        file_size (str): 文件大小。
        device_used (str): 使用设备。
        author_name (str): 作者。
        status (str): 模型加载状态，可以是 "success", "fail" 或 "unknown"。
        font_path_bold (str): 粗体字体文件的路径。
        icons_dir (str): 存放所有图标的目录。
    """

    # --- 卡片和布局参数 (可根据需求调整) ---
    # 所有尺寸参数都乘以2，实现像素翻倍
    card_width = 800 * 2
    card_height = 400 * 2
    card_corner_radius = 1 * 2  # 采纳用户新值并翻倍

    left_rect_x_offset = -150 * 2  # 左侧图片区域向左延伸的距离
    left_rect_y_offset = 50 * 2
    left_rect_width = 300 * 2
    left_rect_height = 300 * 2
    left_rect_corner_radius = 20 * 2

    # 文字起始X坐标 (相对于白色卡片左上角) - 保持与键值对对齐
    text_start_x = 280 * 2
    # 文字起始Y坐标 - 整体上移更多，为顶部标题和状态信息留出空间 (采纳用户新值并翻倍)
    text_start_y_content = 80 * 2

    # 每行文字的垂直间距 - 保持紧凑 (采纳用户新值并翻倍)
    line_spacing = 24 * 2

    # 字体大小也翻倍
    main_title_font_size = 36 * 2  # “模型信息卡”标题字体大小
    info_font_size = 16 * 2  # 所有键值对信息字体大小
    status_font_size = 16 * 2  # 状态文字字体大小

    # --- 准备画布和绘制基础矩形 ---
    total_width = card_width - left_rect_x_offset
    total_height = max(card_height, left_rect_y_offset + left_rect_height)

    canvas = Image.new('RGBA', (total_width, total_height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(canvas)

    card_x = abs(left_rect_x_offset)
    card_y = 0

    # 绘制核心区域的外围圆角矩形 (作为卡片背景)
    draw.rounded_rectangle(
        [(card_x, card_y), (card_x + card_width, card_y + card_height)],
        radius=card_corner_radius,
        fill=(255, 255, 255, 255)
    )

    # --- 处理图片裁剪和粘贴 ---
    if image_path and os.path.exists(image_path):
        try:
            original_image = Image.open(image_path).convert("RGBA")
            img_width, img_height = original_image.size
            target_width, target_height = left_rect_width, left_rect_height

            # 计算缩放比例，确保图片能够完全覆盖目标区域
            scale = max(target_width / img_width, target_height / img_height)

            scaled_width = int(img_width * scale)
            scaled_height = int(img_height * scale)
            # 使用 Image.LANCZOS 进行高质量缩放
            scaled_image = original_image.resize((scaled_width, scaled_height), Image.LANCZOS)

            left = (scaled_width - target_width) / 2
            top = (scaled_height - target_height) / 2
            right = (scaled_width + target_width) / 2
            bottom = (scaled_height + target_height) / 2

            cropped_image = scaled_image.crop((int(left), int(top), int(right), int(bottom)))

            # 应用圆角
            alpha = Image.new('L', cropped_image.size, 0)
            draw_alpha = ImageDraw.Draw(alpha)
            draw_alpha.rounded_rectangle(
                [(0, 0), cropped_image.size],
                radius=left_rect_corner_radius,
                fill=255
            )
            cropped_image.putalpha(alpha)
            rounded_image = cropped_image

            canvas.paste(rounded_image, (int(left_rect_x_offset + card_x), int(left_rect_y_offset)), rounded_image)
        except Exception as e:
            print(f"处理图片 '{image_path}' 时发生错误: {e}。将使用默认占位符。")
            draw.rounded_rectangle(
                [(left_rect_x_offset + card_x, left_rect_y_offset),
                 (left_rect_x_offset + card_x + left_rect_width, left_rect_y_offset + left_rect_height)],
                radius=left_rect_corner_radius,
                fill=(230, 230, 230, 255)
            )
    else:
        print(f"图片文件 '{image_path}' 不存在或未提供。将使用默认占位符。")
        draw.rounded_rectangle(
            [(left_rect_x_offset + card_x, left_rect_y_offset),
             (left_rect_x_offset + card_x + left_rect_width, left_rect_y_offset + left_rect_height)],
            radius=left_rect_corner_radius,
            fill=(230, 230, 230, 255)
        )

    # --- 加载字体 ---
    try:
        main_title_font = ImageFont.truetype(font_path_bold, main_title_font_size)
        info_font = ImageFont.truetype(font_path_bold, info_font_size)
        status_font = ImageFont.truetype(font_path_bold, status_font_size)
    except IOError:
        print(f"错误: 找不到字体文件 '{font_path_bold}' 或字体文件损坏。")
        print("请确保你提供的字体文件存在且路径正确。")
        return None

    # --- 绘制主标题“模型信息卡” ---
    main_title_text = "模型信息卡"
    main_title_bbox = main_title_font.getbbox(main_title_text)
    main_title_width = main_title_bbox[2] - main_title_bbox[0]
    main_title_x = card_x + card_width / 2 - main_title_width / 2
    main_title_y = 10 * 2  # 靠近卡片顶部，并翻倍

    draw.text(
        (int(main_title_x), int(main_title_y)),
        main_title_text,
        fill=TITLE_COLOR,
        font=main_title_font
    )

    # --- 绘制文字和图标 ---
    current_y_pos = text_start_y_content  # 从实际内容开始的Y坐标

    # 模型名称
    draw_icon_and_text(
        draw, canvas,
        os.path.join(icons_dir, "icon-model.png"),
        f"模型名称：{model_name}",
        info_font,
        (card_x + text_start_x, current_y_pos),
        DEFAULT_TEXT_COLOR
    )
    current_y_pos += info_font_size + line_spacing

    # 训练日期
    draw_icon_and_text(
        draw, canvas,
        os.path.join(icons_dir, "icon-date.png"),
        f"训练日期：{train_date}",
        info_font,
        (card_x + text_start_x, current_y_pos),
        DEFAULT_TEXT_COLOR
    )
    current_y_pos += info_font_size + line_spacing

    # 模型版本
    draw_icon_and_text(
        draw, canvas,
        os.path.join(icons_dir, "icon-version.png"),
        f"模型版本：{model_version}",
        info_font,
        (card_x + text_start_x, current_y_pos),
        DEFAULT_TEXT_COLOR
    )
    current_y_pos += info_font_size + line_spacing

    # 模型格式
    draw_icon_and_text(
        draw, canvas,
        os.path.join(icons_dir, "icon-format.png"),
        f"模型格式：{model_format}",
        info_font,
        (card_x + text_start_x, current_y_pos),
        DEFAULT_TEXT_COLOR
    )
    current_y_pos += info_font_size + line_spacing

    # 文件大小
    draw_icon_and_text(
        draw, canvas,
        os.path.join(icons_dir, "icon-size.png"),
        f"文件大小：{file_size}",
        info_font,
        (card_x + text_start_x, current_y_pos),
        DEFAULT_TEXT_COLOR
    )
    current_y_pos += info_font_size + line_spacing

    # 使用设备
    draw_icon_and_text(
        draw, canvas,
        os.path.join(icons_dir, "icon-device.png"),
        f"使用设备：{device_used}",
        info_font,
        (card_x + text_start_x, current_y_pos),
        DEFAULT_TEXT_COLOR
    )
    current_y_pos += info_font_size + line_spacing

    # 作者
    draw_icon_and_text(
        draw, canvas,
        os.path.join(icons_dir, "icon-author.png"),
        f"作    者：{author_name}",
        info_font,
        (card_x + text_start_x, current_y_pos),
        DEFAULT_TEXT_COLOR
    )
    current_y_pos += info_font_size + line_spacing  # 采纳用户新值，作者和状态信息间距不需要太宽

    # --- 绘制状态信息 (与作者等信息左对齐，带图标) ---
    status_text_content = ""
    status_icon_path = ""
    status_color = DEFAULT_TEXT_COLOR  # 默认灰色

    if status == "success":
        status_text_content = "模型已被正确加载，等待您后续操作"
        status_icon_path = os.path.join(icons_dir, "icon-success.png")
        status_color = STATUS_SUCCESS_COLOR
    elif status == "fail":
        status_text_content = "模型加载失败，请重新上传模型"
        status_icon_path = os.path.join(icons_dir, "icon-fail.png")
        status_color = STATUS_FAIL_COLOR
    else:  # 增加了 "unknown" 状态
        status_text_content = "状态未知"
        status_icon_path = os.path.join(icons_dir, "icon-unknown.png")
        status_color = STATUS_UNKNOWN_COLOR  # 未知状态颜色

    # 状态信息现在直接使用 draw_icon_and_text 绘制，与上方键值对对齐
    draw_icon_and_text(
        draw, canvas,
        status_icon_path,  # 传入对应的状态图标路径
        status_text_content,
        status_font,
        (card_x + text_start_x, current_y_pos),  # 保持与键值对相同的X起始位置
        status_color
    )

    return canvas

# --- 示例用法 ---
if __name__ == "__main__":
    # 用户提供的所有信息
    default_fallback_font = "LXGWWenKai-Bold.ttf" # 如果自定义字体不存在，则回退到这个
    image_to_use = r"C:\Users\<USER>\Desktop\Pyside6\resources\assets\images\233.png" # 你的图片路径
    font_to_use = r"C:\Users\<USER>\Desktop\Pyside6\resources\assets\fonts\Source_Han_Sans_SC_Bold.otf"


    icons_folder = r"C:\Users\<USER>\Desktop\Pyside6\resources\assets\icons"
    required_icons = ["icon-date.png", "icon-device.png", "icon-format.png",
                    "icon-model.png", "icon-size.png", "icon-version.png", "icon-author.png"]
    # --- END 字体和图标路径检查 ---


    model_name_info = "新一代自然语言处理模型"
    train_date_info = "2024年10月26日"
    model_version_info = "v2.1 Beta"
    model_format_info = "PyTorch (.pt)"
    file_size_info = "2.5 GB"
    device_used_info = "NVIDIA RTX 4090"
    author_name_info = "AI研究团队"
    # 可以是 "success", "fail" 或 "unknown"
    model_status_info = "success" # 更改为 "success" 或 "unknown" 试试看

    # 调用主生成函数
    final_card = generate_model_card(
        image_to_use,
        model_name_info,
        train_date_info,
        model_version_info,
        model_format_info,
        file_size_info,
        device_used_info,
        author_name_info,
        model_status_info, # 传入状态信息
        font_path_bold=font_to_use, # 传入已验证的字体路径
        icons_dir=icons_folder
    )

    if final_card:
        output_filename = "model_info_card_final.png"
        final_card.save(output_filename)
        print(f"模型信息卡片已成功生成并保存为 '{output_filename}'")
    else:
        print("模型信息卡片生成失败。")

if __name__ == "__main__":
    run_code = 0

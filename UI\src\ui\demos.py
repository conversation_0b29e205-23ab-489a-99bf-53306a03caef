# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'demos.ui'
##
## Created by: Qt User Interface Compiler version 6.9.0
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QC<PERSON>alGradient, Q<PERSON>ursor,
    Q<PERSON>ont, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QHBoxLayout, QLabel, QMainWindow,
    QPushButton, QSizePolicy, QVBoxLayout, QWidget)
import resources_rc

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(1195, 620)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.horizontalLayout = QHBoxLayout(self.centralwidget)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.mainLayout = QVBoxLayout()
        self.mainLayout.setObjectName(u"mainLayout")
        self.topContainerWidget = QWidget(self.centralwidget)
        self.topContainerWidget.setObjectName(u"topContainerWidget")
        sizePolicy = QSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(self.topContainerWidget.sizePolicy().hasHeightForWidth())
        self.topContainerWidget.setSizePolicy(sizePolicy)
        self.topContainerWidget.setMinimumSize(QSize(0, 50))
        self.topContainerWidget.setMaximumSize(QSize(16777215, 50))
        self.horizontalLayout_2 = QHBoxLayout(self.topContainerWidget)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.topBarLayout = QHBoxLayout()
        self.topBarLayout.setObjectName(u"topBarLayout")
        self.titleBarWidget = QWidget(self.topContainerWidget)
        self.titleBarWidget.setObjectName(u"titleBarWidget")
        sizePolicy.setHeightForWidth(self.titleBarWidget.sizePolicy().hasHeightForWidth())
        self.titleBarWidget.setSizePolicy(sizePolicy)
        self.titleBarWidget.setMinimumSize(QSize(0, 50))
        self.titleBarWidget.setMaximumSize(QSize(16777215, 50))
        self.horizontalLayout_5 = QHBoxLayout(self.titleBarWidget)
        self.horizontalLayout_5.setSpacing(6)
        self.horizontalLayout_5.setObjectName(u"horizontalLayout_5")
        self.title_bar_main_layout = QHBoxLayout()
        self.title_bar_main_layout.setSpacing(0)
        self.title_bar_main_layout.setObjectName(u"title_bar_main_layout")
        self.appTitleLabel = QLabel(self.titleBarWidget)
        self.appTitleLabel.setObjectName(u"appTitleLabel")
        self.appTitleLabel.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.title_bar_main_layout.addWidget(self.appTitleLabel)

        self.buttons_layout = QHBoxLayout()
        self.buttons_layout.setSpacing(5)
        self.buttons_layout.setObjectName(u"buttons_layout")
        self.btn_min = QPushButton(self.titleBarWidget)
        self.btn_min.setObjectName(u"btn_min")
        icon = QIcon()
        icon.addFile(u":/icons/assets/icons/btn_min_white.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.btn_min.setIcon(icon)
        self.btn_min.setIconSize(QSize(20, 20))

        self.buttons_layout.addWidget(self.btn_min)

        self.btn_max = QPushButton(self.titleBarWidget)
        self.btn_max.setObjectName(u"btn_max")
        icon1 = QIcon()
        icon1.addFile(u":/icons/assets/icons/btn_max_white.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.btn_max.setIcon(icon1)
        self.btn_max.setIconSize(QSize(20, 20))

        self.buttons_layout.addWidget(self.btn_max)

        self.btn_close = QPushButton(self.titleBarWidget)
        self.btn_close.setObjectName(u"btn_close")
        icon2 = QIcon()
        icon2.addFile(u":/icons/assets/icons/btn_close_white.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.btn_close.setIcon(icon2)
        self.btn_close.setIconSize(QSize(20, 20))

        self.buttons_layout.addWidget(self.btn_close)


        self.title_bar_main_layout.addLayout(self.buttons_layout)

        self.title_bar_main_layout.setStretch(0, 10)
        self.title_bar_main_layout.setStretch(1, 1)

        self.horizontalLayout_5.addLayout(self.title_bar_main_layout)


        self.topBarLayout.addWidget(self.titleBarWidget)


        self.horizontalLayout_2.addLayout(self.topBarLayout)


        self.mainLayout.addWidget(self.topContainerWidget)

        self.mainContentWidget = QWidget(self.centralwidget)
        self.mainContentWidget.setObjectName(u"mainContentWidget")
        self.horizontalLayout_3 = QHBoxLayout(self.mainContentWidget)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.horizontalLayout_4 = QHBoxLayout()
        self.horizontalLayout_4.setObjectName(u"horizontalLayout_4")
        self.mainBodyLabel = QLabel(self.mainContentWidget)
        self.mainBodyLabel.setObjectName(u"mainBodyLabel")

        self.horizontalLayout_4.addWidget(self.mainBodyLabel)


        self.horizontalLayout_3.addLayout(self.horizontalLayout_4)


        self.mainLayout.addWidget(self.mainContentWidget)

        self.mainLayout.setStretch(0, 1)
        self.mainLayout.setStretch(1, 10)

        self.horizontalLayout.addLayout(self.mainLayout)

        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)

        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"MainWindow", None))
        self.appTitleLabel.setText(QCoreApplication.translate("MainWindow", u"\u6807\u9898\u533a\u57df", None))
        self.btn_min.setText("")
        self.btn_max.setText("")
        self.btn_close.setText("")
        self.mainBodyLabel.setText(QCoreApplication.translate("MainWindow", u"\u6b63\u6587\u533a\u57df", None))
    # retranslateUi


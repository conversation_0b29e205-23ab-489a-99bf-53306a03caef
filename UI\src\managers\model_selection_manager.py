# src/managers/model_selection_manager.py

import os
import re
from pathlib import Path
from PySide6.QtWidgets import QFileDialog, QListWidget, QMainWindow
from PySide6.QtCore import Signal, QObject, QFileInfo, QDateTime
from PySide6.QtGui import QPixmap  # 导入 QPixmap 用于检查图片文件

from src.managers.status_bar_manager import StatusBarManager
from src.managers.log_manager import LogManager
from src.ui.demo import Ui_MainWindow
from src.utils.ui_image_generator import generate_model_card  # 导入生成函数


class ModelSelectionManager(QObject):
    model_selected = Signal(dict)

    # 维护核心 YOLO 版本到背景图字符的映射字典 (保持简洁)
    # 键是 YOLO 版本核心字符串 (例如 "yolov8n", "yolov11m")
    # 值是背景图对应的字符 (例如 "n", "m")
    YOLO_CORE_VERSION_TO_BG_CHAR = {
        "yolov8n": "n",
        "yolov8s": "s",
        "yolov8m": "m",
        "yolov8l": "l",
        "yolov8x": "x",
        "yolov9n": "n",
        "yolov9s": "s",
        "yolov11m": "m",  # 示例：YOLOv11
        # ... 可以继续添加更多 YOLO 核心版本
    }

    # 模型类型后缀到描述的映射字典
    # 键是后缀 (例如 "-seg"), 值是中文描述 (例如 " (实例分割模型)")
    MODEL_TYPE_SUFFIX_TO_DESCRIPTION = {
        "-seg": " (实例分割模型)",
        "-pose": " (姿态检测模型)",
        "-obb": " (定向检测模型)",
        "-cls": " (分类模型)",
        # 如果是标准的检测模型，则不加后缀，默认为 " (目标检测模型)"
        # 其他未知后缀默认不添加描述
    }

    def __init__(self, main_window: QMainWindow, ui: Ui_MainWindow,
                 status_bar_manager: StatusBarManager, log_manager: LogManager):
        super().__init__()
        self.main_window = main_window
        self.ui = ui
        self.status_bar_manager = status_bar_manager
        self.log_manager = log_manager
        self._model_path = None

        # 获取主窗口的资源路径函数
        self.get_app_resource_path = getattr(main_window, 'get_resource_path', None)
        if self.get_app_resource_path is None:
            self.log_manager.append_log_message("ERROR", "ModelSelection", "初始化失败：无法获取主窗口的资源路径函数！")
            # 可以在这里抛出异常或采取其他错误处理措施

        self._connect_signals()

    def _connect_signals(self):
        self.ui.qlist.itemClicked.connect(self._handle_list_item_clicked)

    def _handle_list_item_clicked(self, item):
        clicked_text = item.text()
        if clicked_text == "模型选择":
            self.select_model_file()

    def select_model_file(self):
        self.log_manager.append_log_message("INFO", "ModelSelection", "正在打开模型选择对话框...")
        options = QFileDialog.Options()
        file_path, _ = QFileDialog.getOpenFileName(
            self.main_window,
            "选择模型文件",
            "",
            "模型文件 (*.pt *.pth *.onnx *.pb *.engine *.weights);;所有文件 (*)",
            options=options
        )

        if file_path:
            self.set_model_path(file_path)
            model_info = self._parse_model_info(file_path)

            # --- 生成或获取模型信息卡片图片 ---
            if self.get_app_resource_path:
                # 获取放置生成图片的目标目录
                target_images_dir = self.get_app_resource_path("resources/assets/images")
                os.makedirs(target_images_dir, exist_ok=True)  # 确保目录存在

                # 定义生成的卡片文件名：直接使用模型文件的基础名称，然后添加 _card.png 扩展名
                base_file_name_without_ext = Path(file_path).stem  # 获取不带扩展名的文件名
                output_card_path = os.path.join(target_images_dir, f"{base_file_name_without_ext}_card.png")

                # 先检查目标路径是否已存在有效的模型卡片图片
                if os.path.exists(output_card_path) and not QPixmap(output_card_path).isNull():
                    self.log_manager.append_log_message("INFO", "ModelSelection",
                                                        f"模型卡片已存在，直接加载: {output_card_path}")
                    model_info['card_image_path'] = output_card_path
                else:
                    self.log_manager.append_log_message("INFO", "ModelSelection",
                                                        f"模型卡片不存在或已损坏，正在生成: {output_card_path}")

                    # >>> 根据 'version_for_bg' 字段选择背景图 <<<
                    # 确保是小写，这样可以直接拼接成 n.png, s.png, un.png 等
                    version_for_bg_char = model_info.get('version_for_bg', 'un').lower()
                    background_image_name = f"{version_for_bg_char}.png"

                    # 直接使用拼接后的路径，不再进行 os.path.exists 检查，因为用户保证文件存在
                    image_to_use = self.get_app_resource_path(f"resources/assets/images/{background_image_name}")

                    icons_dir = self.get_app_resource_path("resources/assets/icons")  # 图标目录
                    font_to_use = self.get_app_resource_path("resources/assets/fonts/Source_Han_Sans_SC_Bold.otf")

                    author_name_info = "AzureKite"
                    model_status_info = "success"

                    generated_card_image = generate_model_card(
                        image_path=image_to_use,  # 使用动态选择的背景图
                        model_name=model_info['name'],
                        train_date=model_info['training_time'],
                        model_version=model_info['version'],  # 这里的 model_version 是用于卡片显示的
                        model_format=model_info['format'],
                        file_size=f"{model_info['size_mb']:.2f} MB",
                        device_used=model_info['device_suitability'],
                        author_name=author_name_info,
                        status=model_status_info,
                        font_path_bold=font_to_use,
                        icons_dir=icons_dir
                    )

                    if generated_card_image:
                        generated_card_image.save(output_card_path)
                        model_info['card_image_path'] = output_card_path
                    else:
                        self.log_manager.append_log_message("ERROR", "ModelSelection", "模型信息卡片图片生成失败。")
                        model_info['card_image_path'] = ""

            else:
                self.log_manager.append_log_message("WARNING", "ModelSelection",
                                                    "无法生成模型卡片图片，因为资源路径函数不可用。")
                model_info['card_image_path'] = ""

            self.log_manager.append_log_message("INFO", "ModelSelection", f"模型文件已选择: {model_info['name']}")
            self.status_bar_manager.update_model_display(f"{model_info['name']}")
            self.model_selected.emit(model_info)
        else:
            self.log_manager.append_log_message("WARNING", "ModelSelection", "模型选择已取消。")
            self.status_bar_manager.update_status_message("模型选择已取消。")

    def _parse_model_info(self, file_path: str) -> dict:
        """
        解析模型文件路径，提取相关信息。
        专注于处理以下规范命名模式：
        1. 'trainX-YYYYMMDD-HHMMSS-yolovY[nslmx][-任务后缀]-best.pt' 或 'trainX-YYYYMMSS-yolovY[nslmx][-任务后缀]-last.pt'
           （例如：'train6-20250523-175204-yolo11m-best.pt' 或 'train6-20250523-175204-yolo11m-seg-best.pt'）
        2. 'best.pt' 或 'last.pt'
        通过正则表达式提取日期和完整的模型名称部分，并根据模型名称中的后缀判断模型类型和背景图字符。
        """
        file_info = QFileInfo(file_path)
        file_name = file_info.fileName()
        file_suffix = file_info.suffix()
        base_name = file_info.baseName()

        training_time = "N/A"
        model_version_display = "未知版本"  # 用于显示的完整版本 (例如 "YOLOv8S-SEG (实例分割模型)")
        version_for_bg = "un"  # 用于背景图的字符 (例如 's', 'm', 'n')
        model_format = file_suffix.upper()

        # 尝试匹配完整格式的文件名
        # 模式: (任意前缀-)?(YYYYMMDD-HHMMSS)-(模型名称部分)-(best|last)
        # 模型名称部分 (?P<model_name_part>.*?) 可以包含 'yolov\d+[nslmx]' 后面跟着任意字符（如 -seg）
        match = re.search(
            r'(?:.*?-)?(?P<time_date>\d{8})-(?P<time_hour>\d{6})-(?P<model_name_part>.*?)-(?P<status>best|last)$',
            base_name,
            re.IGNORECASE
        )

        if match:
            # 提取训练时间
            training_date_str = match.group('time_date')
            training_hour_str = match.group('time_hour')
            full_time_str = f"{training_date_str}-{training_hour_str}"
            try:
                time_obj = QDateTime.fromString(full_time_str, "yyyyMMdd-hhmmss")
                if time_obj.isValid():
                    training_time = time_obj.toString("yyyy年MM月dd日 hh时mm分ss秒")
                else:
                    self.log_manager.append_log_message("WARNING", "ModelSelection",
                                                        f"解析训练时间无效: {full_time_str}")
            except Exception as e:
                self.log_manager.append_log_message("WARNING", "ModelSelection",
                                                    f"解析训练时间异常: {full_time_str}, 错误: {e}")

            # 提取完整的模型名称部分（例如 'yolo11m' 或 'yolo11m-seg'）
            model_name_part_lower = match.group('model_name_part').lower()
            model_version_base_upper = model_name_part_lower.upper()  # 用于显示的基准名称 (例如 YOLO11M-SEG)

            # --- 判断模型类型并生成描述 ---
            model_description_suffix = ""
            found_type = False
            for suffix, description in self.MODEL_TYPE_SUFFIX_TO_DESCRIPTION.items():
                if model_name_part_lower.endswith(suffix):
                    model_description_suffix = description
                    found_type = True
                    break  # 找到匹配的后缀就停止

            if not found_type and re.search(r'yolov\d+[nslmx]', model_name_part_lower):
                # 如果没有特定的后缀，但匹配到 YOLO 版本模式，则默认为目标检测模型
                model_description_suffix = " (目标检测模型)"

            # 构造最终的模型版本显示字符串
            model_version_display = f"{model_version_base_upper}{model_description_suffix}"

            # --- 确定背景图字符 ---
            # 提取核心 YOLO 版本 (例如 "yolov8n", "yolo11m") 用于查找背景图字符
            # 再次使用正则，因为它能灵活处理各种 YOLO 版本格式，即使没有后缀。
            yolo_core_version_match = re.search(r'yolov\d*[nslmx]', model_name_part_lower, re.IGNORECASE)
            if yolo_core_version_match:
                yolo_core_version_str = yolo_core_version_match.group(0)
                version_for_bg = self.YOLO_CORE_VERSION_TO_BG_CHAR.get(yolo_core_version_str, "un")
            else:
                version_for_bg = "un"  # 非 YOLO 格式或未匹配到，使用通用背景

        elif base_name.lower() in ("best", "last"):
            # 如果文件名是 "best" 或 "last"
            model_version_display = base_name.capitalize()  # "Best" 或 "Last"
            version_for_bg = "un"
        else:
            # 如果不符合任何已知规范
            model_version_display = base_name.upper()
            version_for_bg = "un"

        size_bytes = os.path.getsize(file_path)
        size_mb = size_bytes / (1024 * 1024)

        device_suitability = "CPU / GPU (通用)"
        if file_suffix.lower() == 'engine':
            device_suitability = "GPU (TensorRT专用)"
        elif file_suffix.lower() in ['pt', 'pth']:
            device_suitability = "CPU / GPU (PyTorch)"
        elif file_suffix.lower() == 'onnx':
            device_suitability = "CPU / GPU (ONNX Runtime)"
        elif file_suffix.lower() == 'pb':
            device_suitability = "CPU / GPU (TensorFlow)"

        return {
            "path": file_path,
            "name": file_name,
            "training_time": training_time,
            "version": model_version_display,  # 使用新的显示版本
            "version_for_bg": version_for_bg,
            "format": model_format,
            "size_mb": size_mb,
            "device_suitability": device_suitability,
        }

    def set_model_path(self, path: str):
        self._model_path = path

    def get_model_path(self) -> str:
        return self._model_path

    @property
    def model_name(self) -> str:
        if self._model_path:
            return QFileInfo(self._model_path).fileName()
        return "未选择模型"
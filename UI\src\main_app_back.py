# -*- coding: utf-8 -*-
# src/main_app.py

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QSizePolicy, QGraphicsDropShadowEffect
)
from PySide6.QtCore import Qt, QPoint, QSize
from PySide6.QtGui import QIcon, QColor

# 导入由 pyside6-uic 生成的 UI 类
from src.ui.demos import Ui_MainWindow


class MyCustomWindow(QMainWindow):
    """
    自定义无边框主窗口类，继承自 QMainWindow。
    实现窗口拖动、最小化、最大化/还原、关闭功能，并应用 QSS 样式。
    """

    def __init__(self):
        super().__init__()

        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setWindowFlags(Qt.Window | Qt.FramelessWindowHint | Qt.WindowSystemMenuHint |
                            Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)

        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)

        self.setContentsMargins(10, 10, 10, 10)

        shadow_effect = QGraphicsDropShadowEffect(self)
        shadow_effect.setBlurRadius(20)
        shadow_effect.setColor(QColor(0, 0, 0, 120))
        shadow_effect.setOffset(0, 0)
        self.setGraphicsEffect(shadow_effect)

        self.apply_stylesheet("resources/assets/styles/main_style.qss")

        self.dragging = False
        self.start_pos = QPoint()

        # --- 垂直居中调整：确保标题栏及其子布局都垂直居中 ---
        # 1. 确保 titleBarWidget 内部的 mainLayout 垂直居中
        # 根据 UI 结构图，title_bar_main_layout 是 titleBarWidget 的主要布局
        if hasattr(self.ui, 'title_bar_main_layout'):
            self.ui.title_bar_main_layout.setAlignment(Qt.AlignVCenter)
            # 此外，也可以尝试给 title_bar_main_layout 设置最小高度，以确保有足够空间进行垂直居中
            # self.ui.title_bar_main_layout.setMinimumHeight(50) # 这个通常由父Widget的minSize控制

        # 2. 确保 buttons_layout 内部的按钮垂直居中
        if hasattr(self.ui, 'buttons_layout'):
            self.ui.buttons_layout.setAlignment(Qt.AlignVCenter)
            # 如果按钮布局内部有间距问题，可以尝试调整其 spacing
            # self.ui.buttons_layout.setSpacing(0) # 例子，根据需求调整


        self.setup_custom_window_features()

        # 调整主体内容区域布局边距 (可选)
        if hasattr(self.ui, 'mainContentWidget') and hasattr(self.ui, 'horizontalLayout_4'):
            self.ui.horizontalLayout_4.setContentsMargins(20, 20, 20, 20)


    def get_resource_path(self, relative_path: str) -> str:
        current_script_path = Path(os.path.abspath(__file__))
        current_script_dir = current_script_path.parent
        project_root = current_script_dir.parent

        resource_full_path = project_root / relative_path
        return str(resource_full_path)

    def apply_stylesheet(self, qss_relative_path: str):
        qss_full_path = self.get_resource_path(qss_relative_path)
        try:
            with open(qss_full_path, "r", encoding="utf-8") as f:
                _style = f.read()
                self.setStyleSheet(_style)
                print(f"成功加载 QSS: {qss_full_path}")
        except FileNotFoundError:
            print(f"错误：QSS 文件未找到: {qss_full_path}")
        except Exception as e:
            print(f"加载 QSS 时发生错误: {e}")

    def resize_maximize(self):
        if self.isMaximized():
            self.showNormal()
        else:
            self.showMaximized()

    def double_clicked_title_bar(self, event):
        if event.button() == Qt.MouseButton.LeftButton:
            self.resize_maximize()

    def mousePressEvent(self, event):
        relative_pos_in_titlebar = self.ui.titleBarWidget.mapFromGlobal(event.globalPosition())
        if event.button() == Qt.LeftButton and self.ui.titleBarWidget.rect().contains(relative_pos_in_titlebar.toPoint()):
            self.dragging = True
            self.start_pos = event.globalPosition().toPoint()
            self.windowHandle().startSystemMove()
            return
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        super().mouseMoveEvent(event)

    def mouseReleaseEvent(self, event):
        self.dragging = False
        super().mouseReleaseEvent(event)

    def setup_custom_window_features(self):
        self.ui.btn_close.clicked.connect(self.close)
        self.ui.btn_min.clicked.connect(self.showMinimized)
        self.ui.btn_max.clicked.connect(self.resize_maximize)

        self.ui.titleBarWidget.mouseDoubleClickEvent = self.double_clicked_title_bar.__get__(self, MyCustomWindow)

        self.ui.btn_min.setIcon(QIcon(self.get_resource_path("resources/assets/icons/btn_min_white.png")))
        self.ui.btn_max.setIcon(QIcon(self.get_resource_path("resources/assets/icons/btn_max_white.png")))
        self.ui.btn_close.setIcon(QIcon(self.get_resource_path("resources/assets/icons/btn_close_white.png")))

        # --- 解决按钮固定宽度问题：强制设置尺寸策略和固定大小 ---
        button_policy = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.ui.btn_min.setSizePolicy(button_policy)
        self.ui.btn_max.setSizePolicy(button_policy)
        self.ui.btn_close.setSizePolicy(button_policy)

        self.ui.btn_min.setFixedSize(35, 35)
        self.ui.btn_max.setFixedSize(35, 35)
        self.ui.btn_close.setFixedSize(35, 35)

        # --- 标题标签对齐调整 ---
        if hasattr(self.ui, 'appTitleLabel'):
            # 如果你想要标题文本完全居中（水平和垂直），请使用 Qt.AlignCenter
            # 否则，如果你希望文本左对齐但垂直居中，请使用 Qt.AlignLeft | Qt.AlignVCenter
            self.ui.appTitleLabel.setAlignment(Qt.AlignCenter) # 修改为水平和垂直都居中
            self.ui.appTitleLabel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
            # 确保 QLabel 能够垂直扩展，以利用父布局的垂直居中设置
            self.ui.appTitleLabel.setMinimumHeight(40) # 至少给标题一个高度空间，确保垂直居中可见

        # --- 主体内容标签对齐调整 ---
        if hasattr(self.ui, 'mainBodyLabel'):
            self.ui.mainBodyLabel.setAlignment(Qt.AlignCenter)
            self.ui.mainBodyLabel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MyCustomWindow()
    window.show()
    sys.exit(app.exec())
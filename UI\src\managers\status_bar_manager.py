# src/managers/status_bar_manager.py

from PySide6.QtWidgets import QStatusBar, QLabel, QMessageBox
from PySide6.QtCore import Qt, QTimer, QDateTime, Slot

class StatusBarManager:
    """
    管理 QMainWindow 自带的底部状态栏，显示各种信息。
    包括版权、状态、模型、GPU、FPS 和实时时间显示。
    """

    def __init__(self, main_window):
        """
        初始化 StatusBarManager。

        Args:
            main_window (QMainWindow): 主窗口实例，用于访问其 statusBar() 方法。
        """
        self.main_window = main_window
        self.statusBar_instance = self.main_window.statusBar()
        self.statusBar_instance.setObjectName("appStatusBar")

        self._create_widgets()
        self._init_time_display()
        self.update_status_message("Ready") # 初始设置状态栏信息

    def _create_widgets(self):
        """
        创建并添加各个信息标签到状态栏。
        """
        self.copyright_label = QLabel("Copyright © 2024 Your Company Name")
        self.copyright_label.setObjectName("copyrightLabel")
        self.statusBar_instance.addPermanentWidget(self.copyright_label, 0)

        self.status_info_label = QLabel("Status: Ready")
        self.status_info_label.setObjectName("statusInfoLabel")
        self.statusBar_instance.addPermanentWidget(self.status_info_label, 1)

        self.model_label = QLabel("Model: N/A")
        self.model_label.setObjectName("modelLabel")
        self.statusBar_instance.addPermanentWidget(self.model_label, 0)

        self.gpu_label = QLabel("GPU: --")
        self.gpu_label.setObjectName("gpuLabel")
        self.statusBar_instance.addPermanentWidget(self.gpu_label, 0)

        self.fps_label = QLabel("FPS: --")
        self.fps_label.setObjectName("fpsLabel")
        self.statusBar_instance.addPermanentWidget(self.fps_label, 0)

        self.time_label = QLabel(self.main_window) # 时间标签，父对象是主窗口
        self.time_label.setObjectName("timeLabel")
        self.statusBar_instance.addPermanentWidget(self.time_label, 0)

        self.statusBar_instance.show()

    def _init_time_display(self):
        """
        初始化并启动时间显示定时器。
        """
        self.time_timer = QTimer(self.main_window) # 定时器父对象是主窗口
        self.time_timer.timeout.connect(self.update_current_time)
        self.time_timer.start(1000)
        self.update_current_time() # 立即更新一次时间

    @Slot()
    def update_current_time(self):
        """
        槽函数：更新状态栏中的当前时间。
        """
        current_time = QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss")
        self.time_label.setText(current_time)

    @Slot(str)
    def update_status_message(self, message: str):
        """
        槽函数：更新状态信息文本。
        """
        self.status_info_label.setText(f"Status: {message}")

    @Slot(float)
    def update_fps_display(self, fps_value: float):
        """
        槽函数：更新FPS显示。
        """
        self.fps_label.setText(f"FPS: {fps_value:.1f}")

    @Slot(str)
    def update_model_display(self, model_name: str):
        """
        槽函数：更新当前模型名称显示。
        """
        self.model_label.setText(f"Model: {model_name}")

    @Slot(str)
    def update_gpu_display(self, gpu_usage: str):
        """
        槽函数：更新GPU使用率显示。
        """
        self.gpu_label.setText(f"GPU: {gpu_usage}")

    @Slot(str)
    def show_error_message(self, error_text: str):
        """
        槽函数：在状态栏显示临时错误信息，并在必要时弹出消息框。
        """
        self.statusBar_instance.showMessage(f"Error: {error_text}", 5000)
        # QMessageBox.critical(self.main_window, "Error", error_text) # uncomment to show critical message box

    def test_status_updates(self):
        """
        模拟状态栏信息的更新。
        此方法用于演示和测试目的，在实际应用中，这些更新应由后台逻辑发出信号。
        """
        print("Starting status bar test updates...")
        self.update_status_message("Initializing...")
        QTimer.singleShot(1000, lambda: self.update_model_display("YOLOv8n"))
        QTimer.singleShot(2000, lambda: self.update_gpu_display("25%"))
        QTimer.singleShot(3000, lambda: self.update_status_message("Loading model..."))
        QTimer.singleShot(4000, lambda: self.update_fps_display(28.7))
        QTimer.singleShot(5000, lambda: self.update_status_message("Ready"))
        QTimer.singleShot(6000, lambda: self.show_error_message("Simulated: Failed to load image!"))
        QTimer.singleShot(8000, lambda: self.update_status_message("Detecting..."))
        print("Status bar test updates scheduled.")
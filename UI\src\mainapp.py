# src/main_app.py

import sys
import os
from pathlib import Path
import shutil

# --- 导入 PySide6 模块 ---
from PySide6.QtWidgets import (
    QApplication,
    QMainWindow,
    QGraphicsDropShadowEffect, QSizePolicy,
)
from PySide6.QtCore import Qt, QPoint, QSize, QTimer, QCoreApplication
from PySide6.QtGui import QColor, QPixmap

# --- 导入项目内部资源 ---
from src.ui.demo import Ui_MainWindow
import resources_rc # 确保这个文件已由 pyside6-rcc 生成

# --- 导入解耦后的管理器 ---
from src.managers.title_bar_manager import TitleBarManager
from src.managers.status_bar_manager import StatusBarManager
from src.managers.log_manager import LogManager
from src.managers.model_selection_manager import ModelSelectionManager


class MyCustomWindow(QMainWindow):
    def __init__(self, parent=None):
        super().__init__(parent)

        # --- 1. 配置窗口基本属性 ---
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setWindowFlags(Qt.Window | Qt.FramelessWindowHint | Qt.WindowSystemMenuHint |
                            Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)

        # --- 2. 加载 UI 文件并设置布局 ---
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)

        # --- 3. 添加窗口阴影效果 ---
        shadow_effect = QGraphicsDropShadowEffect(self)
        shadow_effect.setBlurRadius(20)
        shadow_effect.setColor(QColor(0, 0, 0, 120))
        shadow_effect.setOffset(0, 0)
        self.ui.centralwidget.setGraphicsEffect(shadow_effect)

        # --- 4. 应用 QSS 样式表 ---
        self.apply_stylesheet("resources/assets/styles/main_style.qss")

        # --- 5. 初始化管理器 ---
        self.status_bar_manager = StatusBarManager(self)
        self.status_bar_manager.update_status_message("应用程序已启动。")

        self.title_bar_manager = TitleBarManager(
            self,
            self.ui,
            self.get_resource_path,
            self.status_bar_manager.update_status_message
        )

        self.log_manager = LogManager(self)
        self.log_manager.append_log_message("INFO", "MainApp", "应用已启动。")
        self.log_manager.append_log_message("INFO", "MainApp", "正在加载系统核心模块...")

        # --- 初始化 ModelSelectionManager ---
        self.model_selection_manager = ModelSelectionManager(
            self,
            self.ui,
            self.status_bar_manager,
            self.log_manager
        )
        self.model_selection_manager.model_selected.connect(self._on_model_selected)

        # --- 初始设置 detectresult 和 upload 标签 ---
        self.reset_mid_display()

        # --- 6. 设置主内容标签 (detectresult) 的对齐方式和尺寸策略 ---
        if hasattr(self.ui, 'detectresult'):
            self.ui.detectresult.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.ui.detectresult.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)


        # 拖动功能相关属性
        self.dragging = False
        self.start_pos = QPoint()

    # --- 处理模型选择信号的槽函数 ---
    def _on_model_selected(self, model_info: dict):
        self.log_manager.append_log_message("INFO", "MainApp", f"主程序收到模型选择通知: {model_info['name']}")

        card_image_path = model_info.get('card_image_path')

        # 加载并显示模型信息卡片图片
        # 这里的 card_image_path 是 model_selection_manager 生成的本地文件路径
        if card_image_path and os.path.exists(card_image_path):
            try:
                original_pixmap = QPixmap(card_image_path)
                if not original_pixmap.isNull():
                    # >>> 关键修正：等比例缩放图片以适应 QLabel 尺寸 <<<
                    # self.ui.detectresult.size() 获取 QLabel 当前的可用尺寸
                    scaled_pixmap = original_pixmap.scaled(
                        self.ui.detectresult.size(),
                        Qt.KeepAspectRatio, # 保持宽高比
                        Qt.SmoothTransformation # 平滑缩放，高质量
                    )
                    self.ui.detectresult.setPixmap(scaled_pixmap)
                    self.ui.detectresult.setText("") # 清空任何文本
                    self.ui.detectresult.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.ui.detectresult.setScaledContents(False) # 关闭 QLabel 自动缩放，因为我们已经手动缩放了 pixmap
                    self.log_manager.append_log_message("INFO", "UI", f"模型信息卡片图片已显示并缩放: {card_image_path}")
                else:
                    self.log_manager.append_log_message("ERROR", "UI", f"加载模型卡片图片失败 (无效图片文件): {card_image_path}")
                    self.reset_mid_display() # 回到初始状态
            except Exception as e:
                self.log_manager.append_log_message("ERROR", "UI", f"显示模型卡片图片时发生未预期错误: {e}")
                self.reset_mid_display() # 回到初始状态
        else:
            # 如果没有图片路径或文件不存在（即生成失败），回到初始状态
            self.log_manager.append_log_message("WARNING", "UI", "未找到模型信息卡片图片（生成失败），显示默认欢迎信息。")
            self.reset_mid_display() # 回到初始状态

        # >>> 移除冗余的根据模型大小设置 upload 背景图的逻辑 <<<
        # 始终将 upload QLabel 的背景图设置为默认的 png.png
        # 这里的 png.png 仍然通过 QRC 加载
        self.ui.upload.setPixmap(QPixmap(":/images/assets/images/png.png"))
        self.ui.upload.setScaledContents(True) # 这里可以继续让 QLabel 自动缩放背景图
        self.ui.upload.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.log_manager.append_log_message("INFO", "UI", "upload区域背景图已设置为默认。")


    def reset_mid_display(self):
        """
        重置中间显示区域到初始状态。
        """
        # 设置 detectresult 的初始内容（欢迎语）
        initial_html = QCoreApplication.translate("MainWindow", u"<h1>欢迎使用模型检测应用</h1><p>请点击左侧菜单选择模型进行操作。</p>", None)
        self.ui.detectresult.setText(initial_html)
        self.ui.detectresult.setTextFormat(Qt.RichText)
        self.ui.detectresult.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.ui.detectresult.setPixmap(QPixmap()) # 清空图片，确保只显示文本，而不是旧的卡片图
        self.ui.detectresult.setScaledContents(False) # 关闭 QLabel 自动缩放，如果文本为主

        # 设置 upload 区域的初始图片（你的logo），这是QRC资源
        self.ui.upload.setPixmap(QPixmap(u":/images/assets/images/png.png"))
        self.ui.upload.setScaledContents(True)
        self.ui.upload.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.log_manager.append_log_message("INFO", "UI", "中间显示区域已重置。")


    # --- 辅助方法：资源路径获取 ---
    def get_resource_path(self, relative_path: str) -> str:
        """
        获取资源文件的绝对路径，兼容 PyInstaller 打包。
        相对路径应以项目根目录为基准。
        例如：'resources/assets/images/233.png'
        """
        if getattr(sys, 'frozen', False):
            # PyInstaller 打包后的路径
            base_path = sys._MEIPASS
        else:
            # 开发环境下的路径
            current_script_path = os.path.abspath(__file__)
            current_script_dir = os.path.dirname(current_script_path)
            # 假定项目根目录是 src 目录的父目录
            project_root = Path(current_script_dir).parent
            base_path = str(project_root)

        resource_full_path = Path(base_path) / relative_path
        return str(resource_full_path)

    # --- 辅助方法：应用样式表 ---
    def apply_stylesheet(self, qss_relative_path: str):
        qss_full_path = self.get_resource_path(qss_relative_path)
        try:
            with open(qss_full_path, "r", encoding="utf-8") as f:
                _style = f.read()
                self.setStyleSheet(_style)
                print(f"成功加载 QSS: {qss_full_path}")
        except FileNotFoundError:
            print(f"错误：QSS 文件未找到: {qss_full_path}")
            self.log_manager.append_log_message("ERROR", "AppInit", f"QSS文件未找到: {qss_full_path}")
        except Exception as e:
            print(f"加载 QSS 时发生错误: {e}")
            self.log_manager.append_log_message("ERROR", "AppInit", f"加载QSS时发生错误: {e}")

    # --- 窗口拖动事件处理 ---
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            # 仅当鼠标在 TOP 区域时才允许拖动
            if self.ui.TOP.underMouse():
                self.dragging = True
                self.start_pos = event.globalPosition().toPoint() - self.pos()
                event.accept()

    def mouseMoveEvent(self, event):
        if self.dragging:
            self.move(event.globalPosition().toPoint() - self.start_pos)
            event.accept()

    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.dragging = False
            event.accept()


# --- 应用程序入口 ---
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MyCustomWindow()
    window.show()
    sys.exit(app.exec())
/* resources/assets/styles/main_style.qss */

/* 1. 主窗口整体样式 */
QMainWindow {
    background-color: transparent; /* 主窗口背景透明，以显示阴影和浮动效果 */
    border-radius: 10px; /* 窗口圆角 */
}

/* 2. centralwidget 样式 */
#centralwidget {
    background-color: transparent; /* 中心组件背景透明 */
    border-radius: 10px; /* 确保中心组件也有圆角 */
}

/* 3. 顶部容器 Widget 样式 */
#topContainerWidget {
    background-color: #3e3e3e; /* 顶部区域的背景色，保持不透明 */
    border-top-left-radius: 10px; /* 顶部左侧圆角 */
    border-top-right-radius: 10px; /* 顶部右侧圆角 */
}

/* 4. 自定义标题栏容器样式 (它在 topContainerWidget 内部) */
#titleBarWidget {
    background-color: transparent; /* 标题栏背景透明，以显示 topContainerWidget 的背景 */
}

/* 5. 应用程序标题文本样式 */
QLabel#appTitleLabel {
    color: #ffffff;
    font-size: 16px;
    font-weight: bold;
    /* 移除所有 padding 和 margin，避免干扰布局，让布局管理器负责间距 */
    padding: 0px;
    margin: 0px;
}

/* 6. 控制按钮 (最小化、最大化、关闭) 样式 */
QPushButton#btn_min,
QPushButton#btn_max,
QPushButton#btn_close {
    background-color: transparent;
    border: none;
    padding: 0px; /* 确保无内边距 */
    margin: 4px; /* 确保无外边距 */

    /* 设置按钮的推荐宽高，但实际固定大小由 Python 代码中的 setFixedSize 强制控制 */
    width: 35px;
    height: 35px;

    /* 通过 QSS 设置图标大小 */
    qproperty-iconSize: 20px 20px;
}

/* 按钮悬停时的效果 */
QPushButton#btn_close:hover {
    background-color: #e81123; /* 关闭按钮悬停时显示红色背景 */
    border-radius: 5px; /* 35px / 2 = 17.5px，使其呈圆形 */
}

QPushButton#btn_min:hover,
QPushButton#btn_max:hover {
    background-color: #fbc11a; /* 最小化/最大化按钮悬停时显示灰色背景 */
    border-radius: 5px; /* 保持圆形效果 */
}

/* 按钮按下时的效果 */
QPushButton#btn_close:pressed,
QPushButton#btn_min:pressed,
QPushButton#btn_max:pressed {
    background-color: #777777; /* 按下时的颜色，可根据需求调整 */
}


/* 7. 主体内容区域 (MAIN-BODY) 容器样式 */
#mainContentWidget {
    background-color: #343434; /* 主体内容的背景色 */
    border-bottom-left-radius: 10px; /* 底部左侧圆角 */
    border-bottom-right-radius: 10px; /* 底部右侧圆角 */
}

/* 8. 主体文本 Label 样式 */
QLabel#mainBodyLabel {
    color: #cccccc; /* 文本颜色 */
    font-size: 36px; /* 字体大小 */
    font-weight: bold; /* 字体加粗 */
}
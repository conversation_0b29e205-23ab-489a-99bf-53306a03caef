#!/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :model_select_manager.py
# @Time      :2025/6/4 01:50
# <AUTHOR>
# @Function  :
# src/managers/model_select_manager.py

import os
from PySide6.QtWidgets import QFileDialog, QLabel, QLineEdit, QPushButton, QWidget
from PySide6.QtCore import Signal, QObject, QCoreApplication # 导入 QCoreApplication

class ModelSelectManager(QObject):
    """
    负责管理模型选择区域的 UI 逻辑和交互。
    """
    model_path_selected = Signal(str) # 当模型路径被选择时发出信号

    def __init__(self, parent_window: QWidget, ui_elements: dict, get_resource_path_func):
        """
        初始化模型选择管理器。

        Args:
            parent_window: 主窗口实例，用于 QFileDialog 的父级。
            ui_elements: 包含模型选择相关 UI 元素的字典，例如:
                        {'mselect_name': QLabel, 'lineEdit': QLineEdit, 'selectmodel': QPushButton}
            get_resource_path_func: 获取资源路径的函数，用于加载图标等。
        """
        super().__init__(parent_window)
        self.parent_window = parent_window
        self.ui = ui_elements # 这里直接传入相关 UI 元素，而不是整个 self.ui
        self.get_resource_path = get_resource_path_func

        self._setup_ui_elements()
        self._connect_signals_slots()

    def _setup_ui_elements(self):
        """
        设置模型选择区域的 UI 元素的初始状态和文本。
        """
        # 确保这些 UI 元素存在
        if 'mselect_name' in self.ui and isinstance(self.ui['mselect_name'], QLabel):
            # 由于你在 QSS 中设置了 QLabel#mselect_name 的样式，这里通常不需要再设置颜色和字体
            self.ui['mselect_name'].setText(QCoreApplication.translate("MainWindow", u"模型选择", None))
            # self.ui['mselect_name'].setTextFormat(Qt.TextFormat.PlainText) # 已经确认在 UI 中设置了
            pass # 样式通过 QSS 管理

        if 'lineEdit' in self.ui and isinstance(self.ui['lineEdit'], QLineEdit):
            self.ui['lineEdit'].setPlaceholderText("请输入或选择模型文件路径...")
            # QLineEdit 的样式也通过 QSS 管理

        if 'selectmodel' in self.ui and isinstance(self.ui['selectmodel'], QPushButton):
            # 按钮图标已在 UI 中设置，这里确保其样式通过 QSS 管理
            pass

        # 设置 QLabel#mselect_name 的文本，如果它之前没有设置
        # 在 .ui 文件中，它可能已经被 retranslateUi 设置了。
        # 如果你希望通过 Manager 明确设置，可以在这里添加：
        # self.ui['mselect_name'].setText("模型选择")


    def _connect_signals_slots(self):
        """
        连接 UI 元素的信号到相应的槽函数。
        """
        if 'selectmodel' in self.ui and isinstance(self.ui['selectmodel'], QPushButton):
            self.ui['selectmodel'].clicked.connect(self._on_select_model_clicked)

    def _on_select_model_clicked(self):
        """
        当“选择模型”按钮被点击时，打开文件选择对话框。
        """
        # 你可以根据需要更改文件过滤器
        file_filter = "模型文件 (*.pt *.pth *.onnx *.pb);;所有文件 (*.*)"
        # initial_dir = os.path.expanduser("~") # 初始目录设置为用户主目录
        # 或者指定一个项目内的默认目录
        initial_dir = self.get_resource_path("resources/models") # 假设你的模型放在这里

        model_path, _ = QFileDialog.getOpenFileName(
            self.parent_window,
            "选择模型文件",
            initial_dir,
            file_filter
        )

        if model_path:
            self.ui['lineEdit'].setText(model_path)
            self.model_path_selected.emit(model_path) # 发出信号

    def get_selected_model_path(self) -> str:
        """
        获取当前 QLineEdit 中显示的模型路径。
        """
        if 'lineEdit' in self.ui and isinstance(self.ui['lineEdit'], QLineEdit):
            return self.ui['lineEdit'].text()
        return ""

    def set_model_path(self, path: str):
        """
        外部设置模型路径到 QLineEdit。
        """
        if 'lineEdit' in self.ui and isinstance(self.ui['lineEdit'], QLineEdit):
            self.ui['lineEdit'].setText(path)
if __name__ == "__main__":
    run_code = 0

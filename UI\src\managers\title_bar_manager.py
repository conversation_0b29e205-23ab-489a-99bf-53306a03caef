# src/managers/title_bar_manager.py

from PySide6.QtCore import Qt, QPoint, QSize, Slot
from PySide6.QtGui import QIcon, QMouseEvent
from PySide6.QtWidgets import QSizePolicy

class TitleBarManager:
    """
    管理自定义无边框窗口的标题栏功能。
    包括窗口拖动、最小化、最大化/还原、关闭按钮的事件处理和图标设置。
    """

    def __init__(self, main_window, ui, get_resource_path_func, update_status_message_func):
        """
        初始化 TitleBarManager。

        Args:
            main_window (QMainWindow): 主窗口实例，用于调用其 showMinimized, showMaximized 等方法。
            ui (Ui_MainWindow): 由 demo.ui 生成的 UI 对象，提供对标题栏控件的访问。
            get_resource_path_func (callable): 用于获取资源绝对路径的函数。
            update_status_message_func (callable): 用于更新状态栏消息的函数。
        """
        self.main_window = main_window
        self.ui = ui
        self.get_resource_path = get_resource_path_func
        self.update_status_message = update_status_message_func

        # 窗口拖动相关变量
        self.dragging = False
        self.start_pos = QPoint()

        self._setup_buttons()
        self._setup_layout_alignment()
        self._setup_drag_events()
        self._setup_double_click_event()

    def _setup_buttons(self):
        """
        连接标题栏按钮的信号到槽函数，并设置它们的图标和尺寸。
        """
        self.ui.btn_close.clicked.connect(self.main_window.close)
        self.ui.btn_min.clicked.connect(self.main_window.showMinimized)
        self.ui.btn_max.clicked.connect(self.resize_maximize)

        # 设置按钮图标
        self.ui.btn_min.setIcon(QIcon(self.get_resource_path("resources/assets/icons/btn_min.png")))
        self.ui.btn_max.setIcon(QIcon(self.get_resource_path("resources/assets/icons/btn_max.png")))
        self.ui.btn_close.setIcon(QIcon(self.get_resource_path("resources/assets/icons/btn_close.png")))

        # 设置按钮尺寸和图标尺寸
        button_size = 30
        icon_size = 20
        button_policy = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)

        for btn in [self.ui.btn_min, self.ui.btn_max, self.ui.btn_close]:
            btn.setSizePolicy(button_policy)
            btn.setFixedSize(button_size, button_size)
            btn.setIconSize(QSize(icon_size, icon_size))

        # 设置标题标签 (label) 的对齐方式和尺寸策略
        if hasattr(self.ui, 'label'):
            self.ui.label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.ui.label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Policy.Preferred)

    def _setup_layout_alignment(self):
        """
        优化标题栏内部布局的对齐。
        """
        for layout_name in ['horizontalLayout_2', 'title', 'button', 'topBarLayout', 'top']:
            layout = getattr(self.ui, layout_name, None)
            if layout:
                layout.setAlignment(Qt.AlignVCenter)
                layout.setContentsMargins(0, 0, 0, 0)
                layout.setSpacing(0)

    def _setup_drag_events(self):
        """
        设置鼠标按下、移动、释放事件，用于实现窗口拖动。
        """
        # 将主窗口的鼠标事件处理函数重定向到 TitleBarManager 的方法
        # 注意：这里需要替换主窗口的 mousePressEvent
        self.main_window.original_mousePressEvent = self.main_window.mousePressEvent
        self.main_window.mousePressEvent = self.mousePressEvent

        self.main_window.original_mouseMoveEvent = self.main_window.mouseMoveEvent
        self.main_window.mouseMoveEvent = self.mouseMoveEvent

        self.main_window.original_mouseReleaseEvent = self.main_window.mouseReleaseEvent
        self.main_window.mouseReleaseEvent = self.mouseReleaseEvent

    def _setup_double_click_event(self):
        """
        设置标题栏双击事件，用于快速最大化/还原窗口。
        """
        # 将 TOP 控件的鼠标双击事件连接到 `double_clicked_title_bar` 方法。
        # 这里需要替换 TOP 控件的 mouseDoubleClickEvent
        self.ui.TOP.original_mouseDoubleClickEvent = self.ui.TOP.mouseDoubleClickEvent
        self.ui.TOP.mouseDoubleClickEvent = self.double_clicked_title_bar.__get__(self, TitleBarManager)


    @Slot()
    def resize_maximize(self):
        """
        槽函数：切换窗口的最大化/还原状态。
        """
        if self.main_window.isMaximized():
            self.main_window.showNormal()
            self.ui.btn_max.setIcon(QIcon(self.get_resource_path("resources/assets/icons/btn_max.png")))
            self.update_status_message("Window Restored")
        else:
            self.main_window.showMaximized()
            self.ui.btn_max.setIcon(QIcon(self.get_resource_path("resources/assets/icons/btn_max2.png")))
            self.update_status_message("Window Maximized")

    def double_clicked_title_bar(self, event: QMouseEvent):
        """
        事件处理函数：处理标题栏双击事件。
        """
        if event.button() == Qt.MouseButton.LeftButton:
            self.resize_maximize()

    def mousePressEvent(self, event: QMouseEvent):
        """
        事件处理函数：处理鼠标按下事件，用于开始窗口拖动。
        """
        relative_pos_in_titlebar = self.ui.TOP.mapFromGlobal(event.globalPosition())

        if event.button() == Qt.LeftButton and self.ui.TOP.rect().contains(relative_pos_in_titlebar.toPoint()):
            self.dragging = True
            self.start_pos = event.globalPosition().toPoint()

            if not self.main_window.isMaximized():
                self.main_window.windowHandle().startSystemMove()
            self.update_status_message("Dragging Window...")
            # Do not call super().mousePressEvent(event) here
            # because we are replacing the main window's method.
            # We want to handle this event exclusively if it's for title bar dragging.
            return
        # If the event was not handled by the title bar manager, pass it to the original handler.
        return self.main_window.original_mousePressEvent(event)

    def mouseMoveEvent(self, event: QMouseEvent):
        """
        事件处理函数：处理鼠标移动事件。
        """
        # We don't need custom dragging logic here since startSystemMove() handles it.
        # Just pass it to the original handler.
        return self.main_window.original_mouseMoveEvent(event)

    def mouseReleaseEvent(self, event: QMouseEvent):
        """
        事件处理函数：处理鼠标释放事件，用于结束窗口拖动。
        """
        self.dragging = False
        if not self.main_window.isMaximized() and not self.main_window.isMinimized():
            self.update_status_message("Ready")
        # Pass it to the original handler.
        return self.main_window.original_mouseReleaseEvent(event)
/* resources/assets/styles/main_style.qss */
@font-face {
    font-family: "LXGWWenKai"; /* <--- 定义一个字体名称，你可以在 QSS 中使用它 */
    src: url(":/fonts/LXGWWenKai-Bold.ttf"); /* <--- 指向你的字体文件的资源路径 */
    /* font-weight 和 font-style 可以根据字体文件的特性进行设置 */
    /* 例如：font-weight: normal; */
    /* 例如：font-style: normal; */
}


/* 1. QMainWindow (主窗口) 样式 */
QMainWindow {
    background-color: transparent; /* **保持完全透明，以便显示窗口阴影** */
    /* QMainWindow本身不直接设置border-radius，让 #centralwidget 来控制整体圆角 */
}

/* 2. #centralwidget 样式 - 整个UI内容的根容器 */
#centralwidget {
    /* **关键：centralwidget 必须有一个不透明的背景色** */
    /* **它将填充整个窗口内容区域，并定义窗口的整体圆角** */
    background-color: transparent; /* **设置一个不透明的背景色作为整个窗口的基底色** */
    border-radius: 8px; /* **统一整个窗口的圆角** */
    padding: 0px; /* 确保无内边距 */
    margin: 0px;  /* 确保无外边距 */
}

/* 3. 顶部容器 Widget 样式 (对应你的 TOP) */
#TOP {
    background-color: #3e3e3e; /* 顶部区域的背景色，保持不透明 */
    /* 顶部圆角与 #centralwidget 的圆角一致 */
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    /* 底部直角，与 #MID 无缝连接 */
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    min-height: 40px; /* 或者你希望的任何高度，例如 50px */
}

/* 4. 自定义标题栏容器样式 (topBarWidget) */
#topBarWidget {
    background-color: transparent; /* 标题栏背景透明，以显示 TOP 的背景 */
    padding: 0px;
    margin: 0px;
}

/* 5. 应用程序标题文本样式 (label) */
QLabel#protitle {
    color: red;
    font-size: 20px;
    font-weight: bold;
    padding: 5px;
    margin: 5px;
    qproperty-alignment: AlignCenter;
}

/* 6. 控制按钮 (最小化、最大化、关闭) 样式 */
QPushButton#btn_min,
QPushButton#btn_max,
QPushButton#btn_close {
    background-color: transparent;
    border: none;
    padding: 4px;
    margin: 2px;
    min-width: 35px;
    min-height: 35px;
    max-width: 35px;
    max-height: 35px;
    qproperty-iconSize: 20px 20px;
}

/* 按钮悬停时的效果 */
QPushButton#btn_close:hover {
    background-color: #e81123;
    border-radius: 4px;
}

QPushButton#btn_min:hover,
QPushButton#btn_max:hover {
    background-color: #555555;
    border-radius: 4px;
}

/* 按钮按下时的效果 */
QPushButton#btn_close:pressed,
QPushButton#btn_min:pressed,
QPushButton#btn_max:pressed {
    background-color: #777777;
    border-radius: 4px;
}

/* 7. 主体内容区域 (MID) 容器样式 */
#MID {
    background-color: #343434; /* 主体内容的背景色，确保不透明 */
    border-radius: 0px; /* 中间区域上下都是直角，与 TOP 和状态栏无缝连接 */
}

/* 8. 主体文本 Label 样式 (label_2) */
QLabel#label_2 {
    color: #cccccc;
    font-size: 36px;
    font-weight: bold;
    qproperty-alignment: AlignCenter;
}

/* --- 9. 系统自带 QStatusBar 整体样式 --- */
QStatusBar#appStatusBar {
    background-color: #3e3e3e; /* **状态栏背景色，设置为与 TOP 区域相同的颜色** */
    /* 底部圆角与 #centralwidget 的圆角一致 */
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
    /* 顶部直角，与 #MID 无缝连接 */
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    min-height: 40px;
    padding: 0px 5px;
    color: #aaaaaa;
}

/* 状态栏中的各个 QLabel 样式 */
QLabel#copyrightLabel,
QLabel#statusInfoLabel,
QLabel#modelLabel,
QLabel#gpuLabel,
QLabel#fpsLabel,
Q#timeLabel {
    color: #e0e0e0;
    font-size: 13px;
    padding: 2px 8px;
    margin: 0px;
    qproperty-alignment: AlignVCenter;
}

/* 特殊标签样式 */
QLabel#copyrightLabel {
    font-weight: normal;
    color: #a0a0a0;
    padding-left: 0px;
}

QLabel#statusInfoLabel {
    font-weight: bold;
    color: #ffffff;
}

QLabel#modelLabel {
    font-weight: normal;
    color: #90ee90;
}

QLabel#gpuLabel {
    font-weight: normal;
    color: #add8e6;
}

QLabel#fpsLabel {
    font-weight: bold;
    color: #ffff00;
}

QLabel#timeLabel {
    font-weight: normal;
    color: #ffffff;
    padding-right: 0px;
}

/* ----------------------------------------------------- */
/* --- 优化：日志输出区域 (logOutputTextEdit) 样式 --- */
/* ----------------------------------------------------- */
QPlainTextEdit#logOutputTextEdit {
    background-color: #343434; /* 确保与 #MID 背景色完全一致，无缝连接 */
    color: #f0f0f0; /* 这个颜色将是QPlainTextEdit的默认文本颜色，但大部分内容会被HTML内联样式覆盖 */
    border: none; /* 移除边框 */
    border-radius: 0px; /* 移除圆角 */
    font-family: "LXGWWenKai", "Consolas", "Cascadia Code PL", "Courier New", monospace;
    font-size: 12pt;
    font-weight: normal;
    padding: 4px 5px; /* 间接调整行高 */

    /* *** 关键调整：显式覆盖可能存在的背景和边框样式 *** */
    /* 这会尝试确保QPlainTextEdit内部的任何文本块不会有额外的背景 */
    selection-background-color: #0078D7; /* 选中时的背景色，默认蓝色 */
    selection-color: white; /* 选中时的文字颜色 */
}

/* 为了防止其他地方的QLabel样式影响QPlainTextEdit内部的文本，
我们可以尝试更精确地针对QPlainTextEdit内部的通用文本渲染进行样式清除。
但这需要了解Qt如何将HTML渲染成其内部控件。
有时，直接针对 QPlainTextEdit 的子元素进行样式重置是有效的。
例如，如果 Qt 内部将某些文本片段渲染为 QLabel，那么：
*/
QPlainTextEdit#logOutputTextEdit QLabel {
    background-color: transparent; /* 确保内部QLabel背景透明 */
    border: none; /* 确保内部QLabel无边框 */
    border-radius: 0px; /* 确保内部QLabel无圆角 */
    padding: 0px; /* 确保内部QLabel无额外内边距 */
    margin: 0px; /* 确保内部QLabel无额外外边距 */
}


/* 针对滚动条的样式 (保持不变) */
QPlainTextEdit#logOutputTextEdit QScrollBar:vertical {
    border: none;
    background: #3a3a3a;
    width: 8px;
    margin: 0px 0px 0px 0px;
}

QPlainTextEdit#logOutputTextEdit QScrollBar::handle:vertical {
    background: #606060;
    border-radius: 4px;
    min-height: 20px;
}

QPlainTextEdit#logOutputTextEdit QScrollBar::add-line:vertical,
QPlainTextEdit#logOutputTextEdit QScrollBar::sub-line:vertical {
    background: none;
}

QPlainTextEdit#logOutputTextEdit QScrollBar::add-page:vertical,
QPlainTextEdit#logOutputTextEdit QScrollBar::sub-page:vertical {
    background: none;
}



/* ----------------------------------------------------- */
/* --- 主窗口中间区域 (MID) 样式 - 设置透明背景 --- */
/* ----------------------------------------------------- */
#MID {
    background-color: transparent; /* 设置为透明背景 */
    border: none; /* 如果之前有边框，也移除掉，让它完全透明 */
    /* 移除任何 padding 或 margin，让其子元素直接填充 */
    padding: 0px;
    margin: 0px;
}

#midl{
    background-color: transparent; /* 确保有背景色 */
}



#listwidget {
    background-color: #343434; /* 整个列表的深灰色背景 */
    border-radius: 2px; /* 更大的圆角，使其更像药丸状 */
    border: none;  /* 去掉整个控件的边框 */
    outline: none; /* 去掉聚焦时的虚线框，保持简洁 */
    padding: 0; /* 移除内边距，让item可以贴边 */
}


/* 设置整个QListWidget的背景和边框 */
QListWidget {
    background-color: #343434; /* 整个列表的深灰色背景 */
    border: none;  /* 去掉整个控件的边框 */
    outline: none; /* 去掉聚焦时的虚线框，保持简洁 */
    padding: 0; /* 移除内边距，让item可以贴边 */
}

/* 设置QListWidget中每个Item的默认样式 */
QListWidget::item {
    background-color: #373737; /* 每个Item的默认背景色，比列表背景略亮，形成层次感 */
    color: #f0f0f0; /* 默认文字颜色，接近纯白 */
    font-family: "LXGWWenKai"; /* 保持字体 */
    padding: 10px 0px; /* 增加内边距，让item显得更饱满，调整左右间距 */
    margin: 2px 0px 2px 5px;
    border: 1px  #616161; /* 去掉Item的边框 */
    border-radius: 2px; /* 去掉圆角，保持方形 */
    text-align: center; /* 文字水平居中 */
    qproperty-alignment: AlignHCenter ; /* 图标和文字的整体居中 */
}

/* 设置鼠标悬停在Item上的样式 */
QListWidget::item:hover {
    background-color: #4a4a4a; /* 鼠标悬停时的背景色，比默认更亮 */
    color: #f0f0f0; /* 悬停时文字颜色保持不变 */
}

/* 设置选中Item的样式 */
QListWidget::item:selected {
    background-color: #555555; /* 选中时的背景色，比悬停时更亮一些 */
    color: #f0f0f0; /* 选中时的文字颜色保持不变 */
    border-left: 3px solid #00aaff; /* 左侧蓝色指示条，根据图片调整颜色和宽度 */
    padding-left: 12px; /* 因为左侧有边框，所以向右偏移2px (15px - 3px = 12px) */
    font-weight: normal; /* 保持文字不加粗，如果图片中是加粗的，可以改回 bold */
}

/* 滚动条样式（根据你的原QSS进行了微调，以匹配整体深色主题） */
QListWidget::verticalScrollBar {
    border: none;
    background: #2e2e2e; /* 滚动条背景与QListWidget背景一致 */
    width: 8px; /* 滚动条宽度 */
}

QListWidget::verticalScrollBar::handle {
    background: #6a6a6a; /* 滚动条滑块颜色 */
    border-radius: 4px; /* 更大的圆角，使其更像药丸状 */
}

QListWidget::verticalScrollBar::add-line:vertical,
QListWidget::verticalScrollBar::sub-line:vertical {
    border: none;
    background: none;
    height: 0px; /* 隐藏上下箭头按钮 */
}

QListWidget::verticalScrollBar::add-page:vertical,
QListWidget::verticalScrollBar::sub-page:vertical {
    background: none;
}


#logowidget {
    background-color: #343434; /* 整个列表的深灰色背景 */
    border-radius: 2px; /* 更大的圆角，使其更像药丸状 */
    border: none;  /* 去掉整个控件的边框 */
    outline: none; /* 去掉聚焦时的虚线框，保持简洁 */
    padding: 0; /* 移除内边距，让item可以贴边 */
}

/* ====================================================================
中间区域样式
==================================================================== */

/* midm (中间主容器 QWidget) */
#midm{
    background-color: transparent; /* 确保有背景色 */
}

/* midmtqw (顶部结果展示区容器 QWidget) */
#midmtqw {
    background-color: #343434; /* 更深的背景色，作为展示区 */
    border: 1px solid #444444; /* 边框 */
    border-radius: 4px; /* 圆角 */
    padding: 5px; /* 内部留白 */
}

/* detectresult (结果显示 QLabel) */
#detectresult {
    color: #E0E0E0; /* 浅灰色文字 */
    font-size: 24px; /* 较大的字体 */
    font-weight: bold; /* 加粗 */
    qproperty-alignment: AlignCenter; /* 确保文字在QLabel中水平垂直居中 */
}

/* midmbqw (底部控件容器 QWidget) */
#midmbqw {
    background-color: #3e3e3e; /* 更深的背景色 */
    border: 1px solid #444444; /* 边框 */
    border-radius: 4px; /* 圆角 */
    padding: 10px; /* 内部留白 */
}

/* progressBar (QProgressBar) */
QProgressBar {
    border: 1px solid #555555; /* 进度条边框 */
    border-radius: 3px; /* 圆角 */
    background-color: #3C3C3C; /* 进度条背景色 */
    text-align: center; /* 进度文本居中 */
    color: #FFFFFF; /* 进度文本颜色 */
    height: 25px; /* 进度条高度 */
    font-size: 14px; /* 进度文本字体大小 */
}

QProgressBar::chunk {
    background-color: #007ACC; /* 进度条填充颜色 */
    border-radius: 2px; /* 填充部分圆角 */
}

/* startb (开始按钮 QPushButton) */
#startb {
    background-color: transparent; /* 绿色背景 */
    color: #FFFFFF; /* 白色文字 */
    border: none; /* 无边框 */
    border-radius: 2px; /* 圆角 */
    font-size: 15px; /* 字体大小 */
    font-weight: bold; /* 加粗 */
}

#startb:hover {
    background-color: #555555; /* 悬停时颜色变深 */
}

#startb:pressed {
    background-color: #d4f6d8; /* 按下时颜色更深 */
}

/* stopb (停止按钮 QPushButton) */
#stopb {
    background-color: transparent;; /* 黄色背景 */
    border: none;
    border-radius: 2px;
    font-size: 15px;
    font-weight: bold;
}

#stopb:hover {
    background-color: #555555;
}

#stopb:pressed {
    background-color: #C69500;
}

/* endp (结束按钮 QPushButton) */
#endp {
    background-color: transparent; /* 红色背景 */
    color: #FFFFFF; /* 白色文字 */
    border: none;
    border-radius: 2px;
    font-size: 15px;
    font-weight: bold;
}

#endp:hover {
    background-color: #555555;
}

#endp:pressed {
    background-color: #edd6d0;
}

/* ====================================================================
左边区域样式
==================================================================== */


#midr{
    background-color: transparent; /* 确保有背景色 */
}

#uploadim {
    background-color: #343434; /* 整个列表的深灰色背景 */
    border-radius: 2px; /* 更大的圆角，使其更像药丸状 */
    border: none;  /* 去掉整个控件的边框 */
    outline: none; /* 去掉聚焦时的虚线框，保持简洁 */
    padding: 0; /* 移除内边距，让item可以贴边 */
}

#result {
    background-color: #343434; /* 整个列表的深灰色背景 */
    border-radius: 2px; /* 更大的圆角，使其更像药丸状 */
    border: none;  /* 去掉整个控件的边框 */
    outline: none; /* 去掉聚焦时的虚线框，保持简洁 */
    padding: 0; /* 移除内边距，让item可以贴边 */
}

/* ====================================================================
QTextBrowser 文本浏览器样式
==================================================================== */

QTextBrowser {
    background-color: #343434; /* 深色背景，与中间区域的容器背景统一 */
    color: #ffffff; /* 浅灰色文字，确保在深色背景上清晰可读 */
    border: 1px solid #444444; /* 细边框，与中间区域的容器边框统一 */
    border-radius: 4px; /* 圆角，与中间区域的容器圆角统一 */
    padding: 1px; /* 内边距，让文本不紧贴边缘 */
    font-size: 14px; /* 默认字体大小 */
    line-height: 1.5; /* 行高，增加可读性 */
}

/* QTextBrowser 内部的滚动条样式 (与QListWidget的滚动条样式统一) */
QTextBrowser QScrollBar:vertical {
    border: none;
    background: #3A3A3A; /* 滚动条背景 */
    width: 10px; /* 滚动条宽度 */
}

QTextBrowser QScrollBar::handle:vertical {
    background: #6A6A6A; /* 滚动条滑块颜色 */
    border-radius: 5px;
}

QTextBrowser QScrollBar::add-line:vertical,
QTextBrowser QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

QTextBrowser QScrollBar::add-page:vertical,
QTextBrowser QScrollBar::sub-page:vertical {
    background: none;
}

/* 水平滚动条 (如果QTextBrowser内容太宽，一般不常见) */
QTextBrowser QScrollBar:horizontal {
    border: none;
    background: #3A3A3A;
    height: 10px;
}

QTextBrowser QScrollBar::handle:horizontal {
    background: #6A6A6A;
    border-radius: 5px;
}

QTextBrowser QScrollBar::add-line:horizontal,
QTextBrowser QScrollBar::sub-line:horizontal {
    border: none;
    background: none;
}

QTextBrowser QScrollBar::add-page:horizontal,
QTextBrowser QScrollBar::sub-page:horizontal {
    background: none;
}

#detectresult { /* 假设你的 QLabel 的 objectName 是 detectresult */
    /* 背景色可以和 HTML 内部的div颜色呼应，或设置为透明 */
    background-color: transparent; /* 或者一个浅色 */
    border: none; /* 因为HTML div内部已经有了盒子阴影和背景 */
    padding: 10px; /* 外部内边距，让卡片不紧贴边缘 */
    border-radius: 15px; /* 外层圆角 */
    /* 字体默认设置可以放在这里 */
    font-family: "Microsoft YaHei", sans-serif;
    color: #333;
}
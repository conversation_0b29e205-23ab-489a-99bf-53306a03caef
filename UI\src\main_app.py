# -*- coding: utf-8 -*-
# src/main_app.py

import sys
import os
from pathlib import Path

# --- 导入 PySide6 模块 ---
# 从 PySide6.QtWidgets 模块导入 GUI 控件
from PySide6.QtWidgets import (
    QApplication,              # 应用程序对象，管理GUI的控制流和设置
    QMainWindow,               # 提供一个主应用程序窗口框架
    QSizePolicy,               # 控件的尺寸策略，定义控件在布局中如何伸展和收缩
    QGraphicsDropShadowEffect, # 用于给控件添加阴影效果
    QStatusBar,                # 窗口底部的状态栏
    QLabel,                    # 显示文本或图像的控件
    QMessageBox                # 用于显示标准消息框（如警告、错误）
)
# 从 PySide6.QtCore 模块导入核心非GUI功能
from PySide6.QtCore import (
    Qt,                        # Qt 的枚举类型，包含对齐方式、窗口标志等
    QPoint,                    # 表示二维空间中的一个点
    QSize,                     # 表示二维空间中的一个尺寸（宽度和高度）
    Slot,                      # 用于标识槽函数，可以连接到信号
    QTimer,                    # 提供重复和单次定时器功能
    QDateTime                  # 处理日期和时间
)
# 从 PySide6.QtGui 模块导入图形相关的类
from PySide6.QtGui import (
    QIcon,                     # 用于显示图标
    QColor                     # 用于定义颜色
)

# --- 导入项目内部资源 ---
# 导入由 pyside6-uic 工具从 demo.ui 文件生成的 UI 类
# 确保这个路径 (src.ui.demo) 和类名 (Ui_MainWindow) 是正确的
from src.ui.demo import Ui_MainWindow
# 导入资源文件，这个文件包含了所有在 .qrc 文件中定义的资源（如图片），使其可以通过 :/ 路径访问
# 确保你的 .qrc 文件已经通过 pyside6-rcc 工具编译成 resources_rc.py
import resources_rc


class MyCustomWindow(QMainWindow):
    """
    自定义无边框主窗口类，继承自 QMainWindow。
    实现窗口拖动、最小化、最大化/还原、关闭功能，并应用 QSS 样式。
    包含一个多功能的自定义状态栏。
    """

    def __init__(self, parent=None):
        """
        构造函数：初始化 MyCustomWindow 实例。
        这是窗口创建时首先执行的部分，用于设置窗口的基本属性、加载 UI、应用样式等。
        """
        super().__init__(parent) # 调用 QMainWindow 的构造函数

        # --- 1. 配置窗口基本属性 ---
        # 设置窗口背景透明，允许实现圆角或自定义形状（通常与 QSS 配合使用）
        self.setAttribute(Qt.WA_TranslucentBackground)
        # 设置窗口标志为无边框，并保留系统菜单和最小化/最大化按钮的提示。
        # Qt.FramelessWindowHint 移除系统提供的边框、标题栏和控制按钮。
        # Qt.WindowSystemMenuHint, Qt.WindowMinimizeButtonHint, Qt.WindowMaximizeButtonHint
        # 这些提示虽然移除了系统 UI，但允许 PySide6 内部逻辑识别窗口可以被最小化/最大化。
        self.setWindowFlags(Qt.Window | Qt.FramelessWindowHint | Qt.WindowSystemMenuHint |
                            Qt.WindowMinimizeButtonHint | Qt.WindowMaximizeButtonHint)

        # --- 2. 加载 UI 文件并设置布局 ---
        # 实例化从 demo.ui 生成的 UI 类。这个类包含了所有由 Qt Designer 定义的控件和布局。
        self.ui = Ui_MainWindow()
        # 调用 setupUi 方法，将 UI 定义的控件添加到当前窗口中（即 MyCustomWindow）。
        self.ui.setupUi(self)

        # --- 3. 添加窗口阴影效果 ---
        # 创建一个 QGraphicsDropShadowEffect 对象，用于给窗口添加阴影。
        shadow_effect = QGraphicsDropShadowEffect(self)
        # 设置阴影的模糊半径，数值越大阴影越柔和。
        shadow_effect.setBlurRadius(20)
        # 设置阴影的颜色和透明度 (R, G, B, Alpha)。Alpha 120 表示半透明。
        shadow_effect.setColor(QColor(0, 0, 0, 120))
        # 设置阴影的偏移量 (x, y)。(0, 0) 表示阴影居中在控件下方。
        shadow_effect.setOffset(0, 0)
        # 将阴影效果应用到主窗口的中心控件 (centralwidget)。
        # 这样阴影会显示在整个窗口周围，因为 centralwidget 通常是窗口的主体内容。
        self.ui.centralwidget.setGraphicsEffect(shadow_effect)

        # --- 4. 应用 QSS 样式表 ---
        # 调用辅助方法 `apply_stylesheet` 加载并应用 QSS (Qt Style Sheets) 样式表。
        # QSS 类似于 CSS，用于定义控件的外观。路径是相对于项目根目录的。
        self.apply_stylesheet("resources/assets/styles/main_style.qss")

        # --- 5. 初始化窗口拖动相关变量 ---
        # `dragging` 标志位，当鼠标左键在标题栏按下时设为 True，释放时设为 False。
        self.dragging = False
        # `start_pos` 记录鼠标按下时在屏幕上的全局坐标，用于计算拖动偏移量。
        self.start_pos = QPoint()

        # --- 6. 优化标题栏内部布局对齐 ---
        # 遍历并设置标题栏内部的各个 QHBoxLayout 的垂直居中对齐、移除边距和间距。
        # 这是为了确保标题栏的控件（如标题文本、按钮）紧凑且对齐美观。
        # `hasattr()` 用于安全检查，防止 UI 文件中没有这些布局时出错。
        for layout_name in ['horizontalLayout_2', 'title', 'button', 'topBarLayout', 'top']:
            layout = getattr(self.ui, layout_name, None)
            if layout:
                layout.setAlignment(Qt.AlignVCenter) # 垂直居中对齐
                layout.setContentsMargins(0, 0, 0, 0) # 移除布局内边距
                layout.setSpacing(0)                  # 移除布局中控件间的间距

        # --- 7. 设置自定义窗口的特定功能和控件属性 ---
        # 调用方法 `setup_custom_window_features` 来绑定按钮事件、设置图标和尺寸等。
        # 这部分是自定义标题栏的核心功能实现。
        self.setup_custom_window_features()

        # --- 8. 初始化并使用 QMainWindow 自带的 QStatusBar ---
        # 调用方法 `init_system_status_bar` 来初始化和配置底部状态栏的各个信息显示区域。
        self.init_system_status_bar()
        # 初始设置状态栏的通用状态信息。
        self.update_status_message("Ready")

        # --- 9. 初始化并启动时间更新定时器 ---
        # 调用方法 `init_time_display` 来设置状态栏中显示实时时间的功能。
        self.init_time_display()

        # self.test_status_updates() # 可以在此处取消注释以测试状态栏更新的动态效果。

    # --- 辅助方法：资源路径获取 ---
    def get_resource_path(self, relative_path: str) -> str:
        """
        辅助方法：获取资源的绝对路径。
        这个方法是为了解决 PyInstaller 打包后的路径问题，同时兼容开发环境。
        它假设 `resources` 文件夹位于 `main_app.py` 所在的 `src` 目录的父目录中（即项目根目录）。

        Args:
            relative_path (str): 相对于项目根目录的资源路径，例如 "resources/assets/icons/btn_min.png"。

        Returns:
            str: 资源的绝对路径。
        """
        if getattr(sys, 'frozen', False): # 检查当前脚本是否被 PyInstaller 打包成独立的可执行文件（.exe）
            base_path = sys._MEIPASS      # 如果是，PyInstaller 会将所有资源解压到一个临时目录，`sys._MEIPASS` 指向这个目录
        else:
            # 如果在开发环境中运行（未打包）
            current_script_path = os.path.abspath(__file__) # 获取当前脚本（main_app.py）的绝对路径
            current_script_dir = os.path.dirname(current_script_path) # 获取脚本所在的目录 (src/)
            project_root = Path(current_script_dir).parent # 获取 src 目录的父目录，即项目根目录
            base_path = str(project_root) # 将 Path 对象转换为字符串

        # 将确定的基础路径和传入的相对路径拼接成资源的完整绝对路径
        resource_full_path = Path(base_path) / relative_path
        return str(resource_full_path)

    # --- 辅助方法：应用样式表 ---
    def apply_stylesheet(self, qss_relative_path: str):
        """
        辅助方法：从文件系统加载 QSS 样式表并应用到当前窗口。

        Args:
            qss_relative_path (str): 相对于项目根目录的 QSS 文件路径。
        """
        # 使用 `get_resource_path` 获取 QSS 文件的绝对路径
        qss_full_path = self.get_resource_path(qss_relative_path)

        try:
            with open(qss_full_path, "r", encoding="utf-8") as f:
                _style = f.read() # 读取 QSS 文件所有内容
                self.setStyleSheet(_style) # 将读取到的 QSS 样式内容应用到当前窗口
                print(f"成功加载 QSS: {qss_full_path}")
        except FileNotFoundError:
            print(f"错误：QSS 文件未找到: {qss_full_path}")
        except Exception as e:
            print(f"加载 QSS 时发生错误: {e}")

    # --- 状态栏初始化与更新相关方法 ---
    def init_system_status_bar(self):
        """
        初始化并设置 QMainWindow 自带的底部状态栏。
        在状态栏中添加多个 QLabel 控件以显示不同的信息（版权、状态、模型、GPU、FPS、时间）。
        """
        # 获取 QMainWindow 自动创建的状态栏实例。
        self.statusBar_instance = self.statusBar()
        # 为状态栏设置对象名，以便在 QSS 中进行样式化（例如，可以通过 #appStatusBar 选中）。
        self.statusBar_instance.setObjectName("appStatusBar")

        # --- 创建并添加各个信息标签到状态栏 ---
        # `addPermanentWidget(widget, stretch=0)` 方法：
        # widget: 要添加到状态栏的控件。
        # stretch: 伸展因子。0 表示不伸展（固定大小），大于 0 表示可以伸展，按比例分配剩余空间。

        self.copyright_label = QLabel("Copyright © 2024 Your Company Name")
        self.copyright_label.setObjectName("copyrightLabel")
        self.statusBar_instance.addPermanentWidget(self.copyright_label, 0) # 版权信息，不伸展，靠左

        self.status_info_label = QLabel("Status: Ready")
        self.status_info_label.setObjectName("statusInfoLabel")
        self.statusBar_instance.addPermanentWidget(self.status_info_label, 1) # 状态信息，可伸展，占据中间大部分空间

        self.model_label = QLabel("Model: N/A")
        self.model_label.setObjectName("modelLabel")
        self.statusBar_instance.addPermanentWidget(self.model_label, 0) # 模型信息，不伸展

        self.gpu_label = QLabel("GPU: --")
        self.gpu_label.setObjectName("gpuLabel")
        self.statusBar_instance.addPermanentWidget(self.gpu_label, 0) # GPU 使用，不伸展

        self.fps_label = QLabel("FPS: --")
        self.fps_label.setObjectName("fpsLabel")
        self.statusBar_instance.addPermanentWidget(self.fps_label, 0) # FPS，不伸展

        self.time_label = QLabel(self) # 时间标签，单独创建
        self.time_label.setObjectName("timeLabel")
        self.statusBar_instance.addPermanentWidget(self.time_label, 0) # 时间，不伸展，靠右

        # 显式显示状态栏（通常是默认可见的，但为了代码清晰和调试目的可以显式调用）
        self.statusBar_instance.show()

    def init_time_display(self):
        """
        初始化并启动时间显示定时器。
        创建一个 QTimer，每秒触发一次 timeout 信号，连接到 `update_current_time` 槽函数。
        """
        self.time_timer = QTimer(self)
        self.time_timer.timeout.connect(self.update_current_time) # 连接信号与槽
        self.time_timer.start(1000) # 每 1000 毫秒（1秒）触发一次定时器
        self.update_current_time() # 立即更新一次时间，避免窗口刚显示时时间标签空白

    @Slot() # 使用 @Slot 装饰器明确这是一个槽函数
    def update_current_time(self):
        """
        槽函数：更新状态栏中的当前时间。
        获取当前日期和时间，并格式化后设置给时间标签。
        """
        current_time = QDateTime.currentDateTime().toString("yyyy-MM-dd hh:mm:ss") # 获取当前时间并格式化
        self.time_label.setText(current_time) # 设置时间标签的文本

    @Slot(str) # 声明这个槽函数接收一个字符串参数
    def update_status_message(self, message: str):
        """
        槽函数：更新状态信息文本（常驻在状态栏中）。

        Args:
            message (str): 要显示的状态信息字符串。
        """
        self.status_info_label.setText(f"Status: {message}") # 更新状态标签的文本

    @Slot(float) # 声明这个槽函数接收一个浮点数参数
    def update_fps_display(self, fps_value: float):
        """
        槽函数：更新FPS（每秒帧数）显示。

        Args:
            fps_value (float): 当前的 FPS 值。
        """
        self.fps_label.setText(f"FPS: {fps_value:.1f}") # 格式化为一位小数后更新 FPS 标签

    @Slot(str) # 声明这个槽函数接收一个字符串参数
    def update_model_display(self, model_name: str):
        """
        槽函数：更新当前模型名称显示。

        Args:
            model_name (str): 当前加载的模型名称。
        """
        self.model_label.setText(f"Model: {model_name}") # 更新模型标签的文本

    @Slot(str) # 声明这个槽函数接收一个字符串参数
    def update_gpu_display(self, gpu_usage: str):
        """
        槽函数：更新GPU使用率显示 (例如 "30%", "N/A")。

        Args:
            gpu_usage (str): GPU 使用率字符串。
        """
        self.gpu_label.setText(f"GPU: {gpu_usage}") # 更新 GPU 标签的文本

    @Slot(str) # 声明这个槽函数接收一个字符串参数
    def show_error_message(self, error_text: str):
        """
        槽函数：在状态栏显示临时错误信息，并在必要时弹出消息框。

        Args:
            error_text (str): 错误信息文本。
        """
        # 在状态栏显示消息，5000 毫秒（5秒）后自动清除。
        self.statusBar_instance.showMessage(f"Error: {error_text}", 5000)
        # 也可以弹出一个关键错误对话框，阻塞用户操作直到关闭。
        # QMessageBox.critical(self, "Error", error_text)

    def test_status_updates(self):
        """
        模拟状态栏信息的更新。
        此方法用于演示和测试目的，在实际应用中，这些更新应由后台逻辑发出信号。
        """
        print("Starting status bar test updates...")
        self.update_status_message("Initializing...")
        # 使用 QTimer.singleShot 安排一系列延迟的任务来模拟不同信息源的更新
        QTimer.singleShot(1000, lambda: self.update_model_display("YOLOv8n"))
        QTimer.singleShot(2000, lambda: self.update_gpu_display("25%"))
        QTimer.singleShot(3000, lambda: self.update_status_message("Loading model..."))
        QTimer.singleShot(4000, lambda: self.update_fps_display(28.7))
        QTimer.singleShot(5000, lambda: self.update_status_message("Ready"))
        QTimer.singleShot(6000, lambda: self.show_error_message("Simulated: Failed to load image!"))
        QTimer.singleShot(8000, lambda: self.update_status_message("Detecting..."))
        print("Status bar test updates scheduled.")

    # --- 窗口操作相关方法（最小化、最大化/还原、关闭、拖动） ---
    def resize_maximize(self):
        """
        槽函数：切换窗口的最大化/还原状态。
        根据当前窗口状态调用 `showNormal()` 或 `showMaximized()`。
        同时更新状态栏信息，并改变最大化按钮的图标。
        """
        if self.isMaximized(): # 判断当前窗口是否已最大化
            self.showNormal()  # 如果已最大化，则还原窗口到正常大小
            # 还原窗口后，将最大化按钮图标改回“最大化”图标（即初始图标）
            self.ui.btn_max.setIcon(QIcon(self.get_resource_path("resources/assets/icons/btn_max.png")))
            self.update_status_message("Window Restored") # 更新状态栏信息
        else:
            self.showMaximized() # 如果未最大化，则最大化窗口
            # 最大化窗口后，将最大化按钮图标改为“还原”图标（假设有 `btn_normal.png`）
            # 【注意】你需要确保 `resources/assets/icons/` 路径下有一个名为 `btn_normal.png` 的图标，用于表示还原状态。
            self.ui.btn_max.setIcon(QIcon(self.get_resource_path("resources/assets/icons/btn_max2.png")))
            self.update_status_message("Window Maximized") # 更新状态栏信息

    def double_clicked_title_bar(self, event):
        """
        事件处理函数：处理标题栏双击事件，用于快速最大化/还原窗口。
        当鼠标左键在标题栏区域双击时，触发 `resize_maximize`。

        Args:
            event (QMouseEvent): 鼠标事件对象。
        """
        if event.button() == Qt.MouseButton.LeftButton: # 检查是否是鼠标左键双击
            self.resize_maximize() # 如果是左键双击，则切换最大化/还原状态

    def mousePressEvent(self, event):
        """
        事件处理函数：处理鼠标按下事件，用于开始窗口拖动。
        当鼠标左键在顶部标题栏区域 (`TOP` 控件) 按下时，记录起始位置并启动系统窗口移动。
        这是实现无边框窗口拖拽的关键。

        Args:
            event (QMouseEvent): 鼠标事件对象。
        """
        # 将鼠标的全局位置映射到 `self.ui.TOP` 控件的局部坐标系。
        # 这样可以判断鼠标点击是否发生在 `TOP` 控件的范围内。
        relative_pos_in_titlebar = self.ui.TOP.mapFromGlobal(event.globalPosition())

        # 检查：
        # 1. 鼠标左键是否按下 (`event.button() == Qt.LeftButton`)
        # 2. 鼠标位置是否在 `TOP` 控件的矩形范围内 (`self.ui.TOP.rect().contains(...)`)
        if event.button() == Qt.LeftButton and self.ui.TOP.rect().contains(relative_pos_in_titlebar.toPoint()):
            self.dragging = True                 # 设置拖动标志为 True
            self.start_pos = event.globalPosition().toPoint() # 记录鼠标按下时的全局起始位置

            # 如果窗口不是最大化状态，则调用 `windowHandle().startSystemMove()` 启动系统窗口移动。
            # 这是 PySide6/Qt 推荐的无边框窗口拖动方式，它利用操作系统自身的窗口拖动机制，体验更好、更稳定。
            # 如果窗口已最大化，则通常不允许拖动。
            if not self.isMaximized():
                self.windowHandle().startSystemMove()
            self.update_status_message("Dragging Window...") # 更新状态栏信息
            return # 阻止事件继续传播给父类或其它控件，避免默认处理（如控件被点击）
        super().mousePressEvent(event) # 如果不满足条件（例如点击不在标题栏），则调用父类的 `mousePressEvent`

    def mouseMoveEvent(self, event):
        """
        事件处理函数：处理鼠标移动事件。
        由于我们使用 `startSystemMove()` 来处理拖动，这里通常不需要自定义拖动逻辑。
        如果你需要更精细的自定义拖动行为（例如限制拖动范围），才会在这个方法中添加额外逻辑。

        Args:
            event (QMouseEvent): 鼠标事件对象。
        """
        super().mouseMoveEvent(event) # 调用父类的 `mouseMoveEvent`，确保其他默认行为正常

    def mouseReleaseEvent(self, event):
        """
        事件处理函数：处理鼠标释放事件，用于结束窗口拖动。

        Args:
            event (QMouseEvent): 鼠标事件对象。
        """
        self.dragging = False # 拖动结束，重置拖动标志位
        # 如果窗口既不是最大化也不是最小化状态（即正常窗口），则将状态信息恢复为 "Ready"。
        if not self.isMaximized() and not self.isMinimized():
            self.update_status_message("Ready")
        super().mouseReleaseEvent(event) # 调用父类的 `mouseReleaseEvent`，确保其他默认行为正常

    def setup_custom_window_features(self):
        """
        配置自定义窗口的特定功能和控件属性。
        包括连接按钮信号到槽函数，设置按钮图标和尺寸，以及设置标签的对齐方式和尺寸策略。
        """
        # --- 按钮信号与槽的连接 ---
        self.ui.btn_close.clicked.connect(self.close)             # 关闭按钮点击时调用窗口的 close 方法
        self.ui.btn_min.clicked.connect(self.showMinimized)       # 最小化按钮点击时调用窗口的 showMinimized 方法
        self.ui.btn_max.clicked.connect(self.resize_maximize)     # 最大化按钮点击时调用自定义的 resize_maximize 方法

        # --- 标题栏双击事件绑定 ---
        # 将 TOP 控件（包含整个标题栏区域）的鼠标双击事件连接到 `double_clicked_title_bar` 方法。
        # `.__get__(self, MyCustomWindow)` 是一种 Python 特定的绑定方法的方式，确保 `double_clicked_title_bar`
        # 在被调用时能正确访问 `MyCustomWindow` 实例的属性和方法。
        self.ui.TOP.mouseDoubleClickEvent = self.double_clicked_title_bar.__get__(self, MyCustomWindow)

        # --- 设置按钮图标 ---
        # 使用 `get_resource_path` 辅助函数获取图标的绝对路径，并设置给按钮。
        # 【重要】确保你的 `resources_rc.py` 文件已正确生成，并且这些路径下的图标文件存在。
        self.ui.btn_min.setIcon(QIcon(self.get_resource_path("resources/assets/icons/btn_min.png")))
        self.ui.btn_max.setIcon(QIcon(self.get_resource_path("resources/assets/icons/btn_max.png")))
        self.ui.btn_close.setIcon(QIcon(self.get_resource_path("resources/assets/icons/btn_close.png")))

        # --- 设置按钮尺寸和图标尺寸 ---
        button_size = 30 # 定义按钮的固定尺寸（宽度和高度）
        icon_size = 20   # 定义按钮内部图标的尺寸

        # 创建一个固定尺寸策略，确保按钮不会在布局中伸展或收缩
        button_policy = QSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.ui.btn_min.setSizePolicy(button_policy)
        self.ui.btn_max.setSizePolicy(button_policy)
        self.ui.btn_close.setSizePolicy(button_policy)

        # 设置按钮的固定宽度和高度
        self.ui.btn_min.setFixedSize(button_size, button_size)
        self.ui.btn_max.setFixedSize(button_size, button_size)
        self.ui.btn_close.setFixedSize(button_size, button_size)

        # 设置按钮内部图标的尺寸
        self.ui.btn_min.setIconSize(QSize(icon_size, icon_size))
        self.ui.btn_max.setIconSize(QSize(icon_size, icon_size))
        self.ui.btn_close.setIconSize(QSize(icon_size, icon_size))

        # --- 设置标题标签 (label) 的对齐方式和尺寸策略 ---
        # `hasattr(self.ui, 'label')` 用于安全检查，因为 UI 文件可能变化
        if hasattr(self.ui, 'label'):
            self.ui.label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter) # 左对齐，垂直居中
            # 水平方向上可伸展，垂直方向上按内容自适应（或固定）
            self.ui.label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Policy.Preferred)

        # --- 设置主内容标签 (label_2) 的对齐方式和尺寸策略 ---
        if hasattr(self.ui, 'label_2'):
            self.ui.label_2.setAlignment(Qt.AlignCenter) # 水平垂直都居中对齐
            # 水平垂直方向都可伸展，填充可用空间
            self.ui.label_2.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)


# --- 应用程序入口 ---
if __name__ == "__main__":
    # 1. 创建 QApplication 实例
    # 每个 Qt 应用程序都必须有一个 QApplication 对象。它处理事件循环、窗口管理等。
    app = QApplication(sys.argv) # sys.argv 允许应用程序处理命令行参数

    # 2. 创建自定义主窗口实例
    window = MyCustomWindow()

    # 3. 显示主窗口
    window.show()

    # 4. 启动应用程序的事件循环
    # `app.exec()` (或 `app.exec_()` 在 PySide6 中是 `app.exec()`) 启动了 Qt 的事件循环。
    # 程序进入待机状态，等待用户交互（如点击、输入）或系统事件。
    # 只有当事件循环结束（例如窗口关闭）时，`sys.exit()` 才会执行，退出程序。
    sys.exit(app.exec())
# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'demo.ui'
##
## Created by: Qt User Interface Compiler version 6.9.0
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, Q<PERSON><PERSON>alGradient, Q<PERSON>ursor,
    Q<PERSON>ont, QFontDatabase, QGradient, QIcon,
    QImage, Q<PERSON>eySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QAbstractItemView, QApplication, QHBoxLayout, QLabel,
    QListView, QListWidget, QListWidgetItem, QMainWindow,
    QPlainTextEdit, QProgressBar, QPushButton, QSizePolicy,
    QTextBrowser, QVBoxLayout, QWidget)
import resources_rc

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(1676, 1006)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.horizontalLayout = QHBoxLayout(self.centralwidget)
        self.horizontalLayout.setObjectName(u"horizontalLayout")
        self.horizontalLayout.setContentsMargins(0, 5, 0, 5)
        self.verticalLayout = QVBoxLayout()
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.TOP = QWidget(self.centralwidget)
        self.TOP.setObjectName(u"TOP")
        self.top = QHBoxLayout(self.TOP)
        self.top.setObjectName(u"top")
        self.topBarLayout = QHBoxLayout()
        self.topBarLayout.setObjectName(u"topBarLayout")
        self.topBarWidget = QWidget(self.TOP)
        self.topBarWidget.setObjectName(u"topBarWidget")
        self.horizontalLayout_2 = QHBoxLayout(self.topBarWidget)
        self.horizontalLayout_2.setObjectName(u"horizontalLayout_2")
        self.title = QHBoxLayout()
        self.title.setObjectName(u"title")
        self.protitle = QLabel(self.topBarWidget)
        self.protitle.setObjectName(u"protitle")
        self.protitle.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.title.addWidget(self.protitle)


        self.horizontalLayout_2.addLayout(self.title)

        self.button = QHBoxLayout()
        self.button.setObjectName(u"button")
        self.btn_min = QPushButton(self.topBarWidget)
        self.btn_min.setObjectName(u"btn_min")
        icon = QIcon()
        icon.addFile(u":/icons/assets/icons/btn_min.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.btn_min.setIcon(icon)
        self.btn_min.setIconSize(QSize(20, 20))

        self.button.addWidget(self.btn_min)

        self.btn_max = QPushButton(self.topBarWidget)
        self.btn_max.setObjectName(u"btn_max")
        icon1 = QIcon()
        icon1.addFile(u":/icons/assets/icons/btn_max.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.btn_max.setIcon(icon1)
        self.btn_max.setIconSize(QSize(20, 20))

        self.button.addWidget(self.btn_max)

        self.btn_close = QPushButton(self.topBarWidget)
        self.btn_close.setObjectName(u"btn_close")
        icon2 = QIcon()
        icon2.addFile(u":/icons/assets/icons/btn_close.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.btn_close.setIcon(icon2)
        self.btn_close.setIconSize(QSize(20, 20))

        self.button.addWidget(self.btn_close)


        self.horizontalLayout_2.addLayout(self.button)

        self.horizontalLayout_2.setStretch(0, 9)
        self.horizontalLayout_2.setStretch(1, 1)

        self.topBarLayout.addWidget(self.topBarWidget)


        self.top.addLayout(self.topBarLayout)


        self.verticalLayout.addWidget(self.TOP)

        self.MID = QWidget(self.centralwidget)
        self.MID.setObjectName(u"MID")
        self.mid = QHBoxLayout(self.MID)
        self.mid.setSpacing(2)
        self.mid.setObjectName(u"mid")
        self.mid.setContentsMargins(0, 2, 0, 2)
        self.midlayout = QHBoxLayout()
        self.midlayout.setObjectName(u"midlayout")
        self.midlayout.setContentsMargins(0, 3, 0, 3)
        self.midl = QWidget(self.MID)
        self.midl.setObjectName(u"midl")
        self.midl.setMinimumSize(QSize(260, 0))
        self.midl.setMaximumSize(QSize(260, 16777215))
        self.verticalLayout_2 = QVBoxLayout(self.midl)
        self.verticalLayout_2.setSpacing(3)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.verticalLayout_2.setContentsMargins(0, 0, 3, 0)
        self.midllayout = QVBoxLayout()
        self.midllayout.setObjectName(u"midllayout")
        self.midllayout.setContentsMargins(-1, 2, -1, 2)
        self.listwidget = QWidget(self.midl)
        self.listwidget.setObjectName(u"listwidget")
        self.listwidget.setMinimumSize(QSize(0, 0))
        self.listwidget.setMaximumSize(QSize(16777215, 16777215))
        font = QFont()
        font.setFamilies([u"\u963f\u91cc\u5df4\u5df4\u666e\u60e0\u4f53 2.0"])
        font.setPointSize(13)
        self.listwidget.setFont(font)
        self.verticalLayout_7 = QVBoxLayout(self.listwidget)
        self.verticalLayout_7.setObjectName(u"verticalLayout_7")
        self.qlistlay = QVBoxLayout()
        self.qlistlay.setObjectName(u"qlistlay")
        self.qlist = QListWidget(self.listwidget)
        icon3 = QIcon()
        icon3.addFile(u":/icons/assets/icons/model_select.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        font1 = QFont()
        font1.setPointSize(14)
        font1.setBold(True)
        __qlistwidgetitem = QListWidgetItem(self.qlist)
        __qlistwidgetitem.setFont(font1);
        __qlistwidgetitem.setIcon(icon3);
        icon4 = QIcon()
        icon4.addFile(u":/icons/assets/icons/image.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        __qlistwidgetitem1 = QListWidgetItem(self.qlist)
        __qlistwidgetitem1.setFont(font1);
        __qlistwidgetitem1.setIcon(icon4);
        icon5 = QIcon()
        icon5.addFile(u":/icons/assets/icons/video.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        __qlistwidgetitem2 = QListWidgetItem(self.qlist)
        __qlistwidgetitem2.setFont(font1);
        __qlistwidgetitem2.setIcon(icon5);
        icon6 = QIcon()
        icon6.addFile(u":/icons/assets/icons/camera.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        font2 = QFont()
        font2.setPointSize(14)
        font2.setBold(True)
        font2.setItalic(False)
        __qlistwidgetitem3 = QListWidgetItem(self.qlist)
        __qlistwidgetitem3.setFont(font2);
        __qlistwidgetitem3.setIcon(icon6);
        icon7 = QIcon()
        icon7.addFile(u":/icons/assets/icons/dir.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        __qlistwidgetitem4 = QListWidgetItem(self.qlist)
        __qlistwidgetitem4.setFont(font1);
        __qlistwidgetitem4.setIcon(icon7);
        icon8 = QIcon()
        icon8.addFile(u":/icons/assets/icons/setting.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        __qlistwidgetitem5 = QListWidgetItem(self.qlist)
        __qlistwidgetitem5.setFont(font1);
        __qlistwidgetitem5.setIcon(icon8);
        icon9 = QIcon()
        icon9.addFile(u":/icons/assets/icons/modelmanager.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        __qlistwidgetitem6 = QListWidgetItem(self.qlist)
        __qlistwidgetitem6.setIcon(icon9);
        icon10 = QIcon()
        icon10.addFile(u":/icons/assets/icons/about.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        __qlistwidgetitem7 = QListWidgetItem(self.qlist)
        __qlistwidgetitem7.setFont(font1);
        __qlistwidgetitem7.setIcon(icon10);
        self.qlist.setObjectName(u"qlist")
        font3 = QFont()
        font3.setFamilies([u"LXGW WenKai GB Screen"])
        font3.setPointSize(14)
        font3.setBold(True)
        self.qlist.setFont(font3)
        self.qlist.setLayoutDirection(Qt.LayoutDirection.LeftToRight)
        self.qlist.setLineWidth(2)
        self.qlist.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
        self.qlist.setIconSize(QSize(28, 28))
        self.qlist.setTextElideMode(Qt.TextElideMode.ElideMiddle)
        self.qlist.setMovement(QListView.Movement.Free)
        self.qlist.setProperty(u"isWrapping", False)
        self.qlist.setSpacing(3)
        self.qlist.setViewMode(QListView.ViewMode.ListMode)
        self.qlist.setItemAlignment(Qt.AlignmentFlag.AlignCenter)

        self.qlistlay.addWidget(self.qlist)


        self.verticalLayout_7.addLayout(self.qlistlay)


        self.midllayout.addWidget(self.listwidget)

        self.logowidget = QWidget(self.midl)
        self.logowidget.setObjectName(u"logowidget")
        self.logowidget.setMinimumSize(QSize(260, 260))
        self.logowidget.setMaximumSize(QSize(260, 260))
        self.verticalLayout_6 = QVBoxLayout(self.logowidget)
        self.verticalLayout_6.setObjectName(u"verticalLayout_6")
        self.logolayout = QVBoxLayout()
        self.logolayout.setObjectName(u"logolayout")
        self.logo = QLabel(self.logowidget)
        self.logo.setObjectName(u"logo")
        self.logo.setMinimumSize(QSize(260, 260))
        self.logo.setMaximumSize(QSize(260, 260))
        self.logo.setBaseSize(QSize(350, 350))
        self.logo.setPixmap(QPixmap(u":/images/assets/images/png.png"))
        self.logo.setScaledContents(True)
        self.logo.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.logolayout.addWidget(self.logo)


        self.verticalLayout_6.addLayout(self.logolayout)


        self.midllayout.addWidget(self.logowidget)

        self.midllayout.setStretch(0, 5)
        self.midllayout.setStretch(1, 2)

        self.verticalLayout_2.addLayout(self.midllayout)


        self.midlayout.addWidget(self.midl)

        self.midm = QWidget(self.MID)
        self.midm.setObjectName(u"midm")
        self.midm.setMinimumSize(QSize(1000, 0))
        self.midm.setMaximumSize(QSize(16777215, 16777215))
        self.verticalLayout_4 = QVBoxLayout(self.midm)
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.verticalLayout_4.setContentsMargins(3, 0, 3, 0)
        self.midmt = QHBoxLayout()
        self.midmt.setObjectName(u"midmt")
        self.midmtqw = QWidget(self.midm)
        self.midmtqw.setObjectName(u"midmtqw")
        self.midmtqw.setMinimumSize(QSize(1000, 0))
        self.midmtqw.setMaximumSize(QSize(16777215, 16777215))
        self.verticalLayout_5 = QVBoxLayout(self.midmtqw)
        self.verticalLayout_5.setObjectName(u"verticalLayout_5")
        self.verticalLayout_5.setContentsMargins(2, -1, 2, -1)
        self.detectresult = QLabel(self.midmtqw)
        self.detectresult.setObjectName(u"detectresult")
        self.detectresult.setMinimumSize(QSize(1000, 0))
        self.detectresult.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_5.addWidget(self.detectresult)


        self.midmt.addWidget(self.midmtqw)


        self.verticalLayout_4.addLayout(self.midmt)

        self.midmb = QHBoxLayout()
        self.midmb.setObjectName(u"midmb")
        self.midmbqw = QWidget(self.midm)
        self.midmbqw.setObjectName(u"midmbqw")
        self.horizontalLayout_3 = QHBoxLayout(self.midmbqw)
        self.horizontalLayout_3.setObjectName(u"horizontalLayout_3")
        self.startb = QPushButton(self.midmbqw)
        self.startb.setObjectName(u"startb")
        icon11 = QIcon()
        icon11.addFile(u":/icons/assets/icons/start.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.startb.setIcon(icon11)
        self.startb.setIconSize(QSize(30, 30))

        self.horizontalLayout_3.addWidget(self.startb)

        self.stopb = QPushButton(self.midmbqw)
        self.stopb.setObjectName(u"stopb")
        icon12 = QIcon()
        icon12.addFile(u":/icons/assets/icons/stop.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.stopb.setIcon(icon12)
        self.stopb.setIconSize(QSize(30, 30))

        self.horizontalLayout_3.addWidget(self.stopb)

        self.progressBar = QProgressBar(self.midmbqw)
        self.progressBar.setObjectName(u"progressBar")
        self.progressBar.setValue(24)

        self.horizontalLayout_3.addWidget(self.progressBar)

        self.endp = QPushButton(self.midmbqw)
        self.endp.setObjectName(u"endp")
        icon13 = QIcon()
        icon13.addFile(u":/icons/assets/icons/end.png", QSize(), QIcon.Mode.Normal, QIcon.State.Off)
        self.endp.setIcon(icon13)
        self.endp.setIconSize(QSize(30, 30))

        self.horizontalLayout_3.addWidget(self.endp)


        self.midmb.addWidget(self.midmbqw)


        self.verticalLayout_4.addLayout(self.midmb)

        self.verticalLayout_4.setStretch(0, 10)

        self.midlayout.addWidget(self.midm)

        self.midr = QWidget(self.MID)
        self.midr.setObjectName(u"midr")
        self.midr.setMinimumSize(QSize(400, 0))
        self.midr.setMaximumSize(QSize(400, 16777215))
        self.verticalLayout_3 = QVBoxLayout(self.midr)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.verticalLayout_3.setContentsMargins(3, 0, 0, 3)
        self.midrlay = QVBoxLayout()
        self.midrlay.setObjectName(u"midrlay")
        self.midrlay.setContentsMargins(-1, 2, -1, 2)
        self.uploadim = QWidget(self.midr)
        self.uploadim.setObjectName(u"uploadim")
        self.uploadim.setMinimumSize(QSize(400, 400))
        self.uploadim.setMaximumSize(QSize(400, 400))
        self.uploadim.setLayoutDirection(Qt.LayoutDirection.LeftToRight)
        self.verticalLayout_13 = QVBoxLayout(self.uploadim)
        self.verticalLayout_13.setObjectName(u"verticalLayout_13")
        self.upload = QLabel(self.uploadim)
        self.upload.setObjectName(u"upload")
        self.upload.setMinimumSize(QSize(400, 400))
        self.upload.setMaximumSize(QSize(400, 400))
        self.upload.setLayoutDirection(Qt.LayoutDirection.LeftToRight)
        self.upload.setPixmap(QPixmap(u":/images/assets/images/png.png"))
        self.upload.setScaledContents(True)
        self.upload.setAlignment(Qt.AlignmentFlag.AlignCenter)

        self.verticalLayout_13.addWidget(self.upload)


        self.midrlay.addWidget(self.uploadim)

        self.result = QWidget(self.midr)
        self.result.setObjectName(u"result")
        self.verticalLayout_14 = QVBoxLayout(self.result)
        self.verticalLayout_14.setObjectName(u"verticalLayout_14")
        self.verticalLayout_14.setContentsMargins(2, 0, 0, 0)
        self.textBrowser = QTextBrowser(self.result)
        self.textBrowser.setObjectName(u"textBrowser")

        self.verticalLayout_14.addWidget(self.textBrowser)


        self.midrlay.addWidget(self.result)

        self.midrlay.setStretch(0, 1)
        self.midrlay.setStretch(1, 1)

        self.verticalLayout_3.addLayout(self.midrlay)


        self.midlayout.addWidget(self.midr)

        self.midlayout.setStretch(0, 3)
        self.midlayout.setStretch(1, 9)
        self.midlayout.setStretch(2, 3)

        self.mid.addLayout(self.midlayout)


        self.verticalLayout.addWidget(self.MID)

        self.BUTTOM = QVBoxLayout()
        self.BUTTOM.setObjectName(u"BUTTOM")
        self.logOutputTextEdit = QPlainTextEdit(self.centralwidget)
        self.logOutputTextEdit.setObjectName(u"logOutputTextEdit")

        self.BUTTOM.addWidget(self.logOutputTextEdit)


        self.verticalLayout.addLayout(self.BUTTOM)

        self.verticalLayout.setStretch(1, 10)
        self.verticalLayout.setStretch(2, 2)

        self.horizontalLayout.addLayout(self.verticalLayout)

        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)

        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"MainWindow", None))
        self.protitle.setText(QCoreApplication.translate("MainWindow", u"This is system Name", None))
        self.btn_min.setText("")
        self.btn_max.setText("")
        self.btn_close.setText("")

        __sortingEnabled = self.qlist.isSortingEnabled()
        self.qlist.setSortingEnabled(False)
        ___qlistwidgetitem = self.qlist.item(0)
        ___qlistwidgetitem.setText(QCoreApplication.translate("MainWindow", u"\u6a21\u578b\u9009\u62e9", None));
        ___qlistwidgetitem1 = self.qlist.item(1)
        ___qlistwidgetitem1.setText(QCoreApplication.translate("MainWindow", u"\u56fe\u50cf\u68c0\u6d4b", None));
        ___qlistwidgetitem2 = self.qlist.item(2)
        ___qlistwidgetitem2.setText(QCoreApplication.translate("MainWindow", u"\u89c6\u9891\u68c0\u6d4b", None));
        ___qlistwidgetitem3 = self.qlist.item(3)
        ___qlistwidgetitem3.setText(QCoreApplication.translate("MainWindow", u"\u6444\u50cf\u5934\u68c0\u6d4b", None));
        ___qlistwidgetitem4 = self.qlist.item(4)
        ___qlistwidgetitem4.setText(QCoreApplication.translate("MainWindow", u"\u6587\u4ef6\u5939\u68c0\u6d4b", None));
        ___qlistwidgetitem5 = self.qlist.item(5)
        ___qlistwidgetitem5.setText(QCoreApplication.translate("MainWindow", u"\u68c0\u6d4b\u8bbe\u7f6e", None));
        ___qlistwidgetitem6 = self.qlist.item(6)
        ___qlistwidgetitem6.setText(QCoreApplication.translate("MainWindow", u"\u6a21\u578b\u7ba1\u7406", None));
        ___qlistwidgetitem7 = self.qlist.item(7)
        ___qlistwidgetitem7.setText(QCoreApplication.translate("MainWindow", u"\u5173\u4e8e\u6211\u4eec", None));
        self.qlist.setSortingEnabled(__sortingEnabled)

        self.logo.setText("")
        self.detectresult.setText(QCoreApplication.translate("MainWindow", u"Main Window", None))
        self.startb.setText("")
        self.stopb.setText("")
        self.endp.setText("")
        self.upload.setText("")
        self.textBrowser.setHtml(QCoreApplication.translate("MainWindow", u"<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n"
"<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\n"
"p, li { white-space: pre-wrap; }\n"
"hr { height: 1px; border-width: 0; }\n"
"li.unchecked::marker { content: \"\\2610\"; }\n"
"li.checked::marker { content: \"\\2612\"; }\n"
"</style></head><body style=\" font-family:'\u82f9\u65b9 \u7c97\u4f53'; font-size:12pt; font-weight:400; font-style:normal;\">\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-weight:700;\">Author</span><span style=\" font-weight:500;\">: AzureKite</span></p>\n"
"<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px; font-weight:500;\"><br /></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0"
                        "; text-indent:0px;\"><span style=\" font-weight:700;\">Email</span><span style=\" font-weight:500;\">: <EMAIL></span></p>\n"
"<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px; font-weight:500;\"><br /></p>\n"
"<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\"><span style=\" font-weight:700;\">QQ</span><span style=\" font-weight:500;\">: 910014191</span></p>\n"
"<p style=\"-qt-paragraph-type:empty; margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px; font-weight:500;\"><br /></p></body></html>", None))
    # retranslateUi

